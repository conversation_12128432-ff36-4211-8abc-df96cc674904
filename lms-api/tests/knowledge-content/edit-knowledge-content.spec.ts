import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';
import { KnowledgeContentStatus } from '../../../shared/repositories/lms/constants/enums/knowledge-content.enum';

let knowledgeContentId;
let mediaId;

const updateData = {
  imagePath: './shared/ekyc/valid-id-card.jpg',
  updateTitle: 'updateTitle',
  contentHtml: 'PHAgdGV4dGFsaWduPSJsZWZ0Ij5lZGl0IGRldGFpbDwvcD4=',
  knowledgeContentCodeEdit: 'API2025EDIT',
  instructorIds: 'eb0e0772-00ca-4dd4-8b90-3f1c5471191b',
};

test.beforeEach(
  async ({ knowledgeContentItemsRepo, configuration, authenticationService, knowledgeContentService }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const knowledgeContentTitle = 'API knowledge content';
    const knowledgeContentCode = 'API2025';

    // clean up
    await knowledgeContentItemsRepo.deleteKnowledgeContentItemByCode(knowledgeContentCode);

    //create knowledge content
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'article',
    );
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully created',
        data: expect.objectContaining({
          title: knowledgeContentTitle,
          code: knowledgeContentCode,
          type: 'article',
        }),
      }),
    );
    knowledgeContentId = createKnowledgeContentResp.data.id;
  },
);

test.afterEach(async ({ knowledgeContentItemsRepo }) => {
  await knowledgeContentItemsRepo.deleteKnowledgeContentItemById(knowledgeContentId);
});

test.describe('Positive', () => {
  test(`User with valid permission edit knowledge content general info should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
    mediaService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    //Upload image
    const upload = await mediaService.upload(updateData.imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(updateData.imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'article',
      updateData.contentHtml,
      mediaId,
      updateData.instructorIds,
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );
  });

  test(`User with valid permission edit knowledge content article media should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContentMedia = await knowledgeContentService.updateArticleKnowledgeContent(
      knowledgeContentId,
      updateData.contentHtml,
    );
    await expect(updateKnowledgeContentMedia).toBeOK();
    const updateKnowledgeContentMediaResp = await updateKnowledgeContentMedia.json();
    expect(updateKnowledgeContentMediaResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );
  });

  test(`User with valid permission edit knowledge content ebook media should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const ebookId = '1c6de18d-30df-41e6-9801-2d6821f8396e';

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'ebook',
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );

    const updateKnowledgeContentMedia = await knowledgeContentService.updateEbookKnowledgeContent(
      knowledgeContentId,
      ebookId,
    );
    await expect(updateKnowledgeContentMedia).toBeOK();
    const updateKnowledgeContentMediaResp = await updateKnowledgeContentMedia.json();
    expect(updateKnowledgeContentMediaResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );
  });

  test(`User with valid permission edit knowledge content video media should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const knowledgeContentVideo = configuration.shareKnowledgeContent.mediaContentVideo;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'video',
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );

    const updateKnowledgeContentPlayableMedia = await knowledgeContentService.updateVideoKnowledgeContent(
      knowledgeContentId,
      knowledgeContentVideo.media,
      knowledgeContentVideo.mediaTranscode,
      knowledgeContentVideo.duration,
    );
    await expect(updateKnowledgeContentPlayableMedia).toBeOK();
    const updateKnowledgeContentPlayableMediaResp = await updateKnowledgeContentPlayableMedia.json();
    expect(updateKnowledgeContentPlayableMediaResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );
  });

  test(`User with valid permission edit knowledge content podcast media should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const knowledgeContentPodcast = configuration.shareKnowledgeContent.mediaContentPodcast;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'video',
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );

    const updateKnowledgeContentPlayableMedia = await knowledgeContentService.updatePodcastKnowledgeContent(
      knowledgeContentId,
      knowledgeContentPodcast.media,
      knowledgeContentPodcast.mediaTranscode,
      knowledgeContentPodcast.duration,
    );
    await expect(updateKnowledgeContentPlayableMedia).toBeOK();
    const updateKnowledgeContentPlayableMediaResp = await updateKnowledgeContentPlayableMedia.json();
    expect(updateKnowledgeContentPlayableMediaResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );
  });

  test(`User with valid permission edit knowledge content attachment should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
    mediaService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    //Upload image
    const upload = await mediaService.upload(updateData.imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(updateData.imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContentAttachment = await knowledgeContentService.updateKnowledgeContentAttachment(
      knowledgeContentId,
      mediaId,
    );
    await expect(updateKnowledgeContentAttachment).toBeOK();
    const updateKnowledgeContentAttachmentResp = await updateKnowledgeContentAttachment.json();
    expect(updateKnowledgeContentAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update knowledge content item attachments successfully',
      }),
    );
  });

  test(`User with valid permission edit knowledge content category should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const categoryIds = ['e403e01a-10e8-447f-bf5e-c381e56edea1'];

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContentCategory = await knowledgeContentService.updateKnowledgeContentCategory(
      knowledgeContentId,
      categoryIds,
    );
    await expect(updateKnowledgeContentCategory).toBeOK();
    const updateKnowledgeContentCategoryResp = await updateKnowledgeContentCategory.json();
    expect(updateKnowledgeContentCategoryResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully update knowledge content category',
      }),
    );
  });

  test(`User with valid permission edit knowledge content comment setting should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const isCommentEnabled = false;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContentComment = await knowledgeContentService.updateKnowledgeContentComment(
      knowledgeContentId,
      isCommentEnabled,
    );
    await expect(updateKnowledgeContentComment).toBeOK();
    const updateKnowledgeContentCommentResp = await updateKnowledgeContentComment.json();
    expect(updateKnowledgeContentCommentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully update knowledge content comment',
      }),
    );
  });

  test(`User with valid permission edit knowledge content status should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const currentDate = new Date();
    const publishedStartAt = new Date(new Date().setMonth(currentDate.getDate() + 1));
    const publishedEndAt = new Date(new Date().setMonth(publishedStartAt.getDate() + 2));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContentStatus = await knowledgeContentService.updateKnowledgeContentStatus(
      knowledgeContentId,
      KnowledgeContentStatus.publish,
      publishedEndAt,
      publishedStartAt,
    );
    await expect(updateKnowledgeContentStatus).toBeOK();
    const updateKnowledgeContentStatusResp = await updateKnowledgeContentStatus.json();
    expect(updateKnowledgeContentStatusResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully update status',
      }),
    );

    const getKnowledgeContentDetail = await knowledgeContentService.getKnowledgeContentDetail(knowledgeContentId);
    await expect(getKnowledgeContentDetail).toBeOK();
    const getKnowledgeContentDetailResp = await getKnowledgeContentDetail.json();
    expect(getKnowledgeContentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get knowledge content item successfully',
        data: expect.objectContaining({
          status: KnowledgeContentStatus.scheduled,
        }),
      }),
    );

    const updateKnowledgeContentStatus2 = await knowledgeContentService.updateKnowledgeContentStatus(
      knowledgeContentId,
      KnowledgeContentStatus.revertToDraft,
    );
    await expect(updateKnowledgeContentStatus2).toBeOK();
    const updateKnowledgeContentStatus2Resp = await updateKnowledgeContentStatus2.json();
    expect(updateKnowledgeContentStatus2Resp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully update status',
      }),
    );

    const getKnowledgeContentDetail2 = await knowledgeContentService.getKnowledgeContentDetail(knowledgeContentId);
    await expect(getKnowledgeContentDetail2).toBeOK();
    const getKnowledgeContentDetail2Resp = await getKnowledgeContentDetail2.json();
    expect(getKnowledgeContentDetail2Resp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get knowledge content item successfully',
        data: expect.objectContaining({
          status: KnowledgeContentStatus.draft,
        }),
      }),
    );

    const updateKnowledgeContentStatus3 = await knowledgeContentService.updateKnowledgeContentStatus(
      knowledgeContentId,
      KnowledgeContentStatus.publish,
      publishedEndAt,
      currentDate,
    );
    await expect(updateKnowledgeContentStatus3).toBeOK();
    const updateKnowledgeContentStatus3Resp = await updateKnowledgeContentStatus3.json();
    expect(updateKnowledgeContentStatus3Resp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully update status',
      }),
    );

    const getKnowledgeContentDetail3 = await knowledgeContentService.getKnowledgeContentDetail(knowledgeContentId);
    await expect(getKnowledgeContentDetail3).toBeOK();
    const getKnowledgeContentDetail3Resp = await getKnowledgeContentDetail3.json();
    expect(getKnowledgeContentDetail3Resp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get knowledge content item successfully',
        data: expect.objectContaining({
          status: KnowledgeContentStatus.published,
        }),
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User without valid permission edit knowledge content general should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
    mediaService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    //Upload image
    const upload = await mediaService.upload(updateData.imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(updateData.imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'article',
      updateData.contentHtml,
      mediaId,
      updateData.instructorIds,
    );
    await expect(updateKnowledgeContent).not.toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content article media should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentMedia = await knowledgeContentService.updateArticleKnowledgeContent(
      knowledgeContentId,
      updateData.contentHtml,
    );
    await expect(updateKnowledgeContentMedia).not.toBeOK();
    const updateKnowledgeContentMediaResp = await updateKnowledgeContentMedia.json();
    expect(updateKnowledgeContentMediaResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content ebook media should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const ebookId = '1c6de18d-30df-41e6-9801-2d6821f8396e';

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'ebook',
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentMedia = await knowledgeContentService.updateEbookKnowledgeContent(
      knowledgeContentId,
      ebookId,
    );
    await expect(updateKnowledgeContentMedia).not.toBeOK();
    const updateKnowledgeContentMediaResp = await updateKnowledgeContentMedia.json();
    expect(updateKnowledgeContentMediaResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content video media should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const knowledgeContentVideo = configuration.shareKnowledgeContent.mediaContentVideo;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'video',
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentPlayableMedia = await knowledgeContentService.updateVideoKnowledgeContent(
      knowledgeContentId,
      knowledgeContentVideo.media,
      knowledgeContentVideo.mediaTranscode,
      knowledgeContentVideo.duration,
    );
    await expect(updateKnowledgeContentPlayableMedia).not.toBeOK();
    const updateKnowledgeContentPlayableMediaResp = await updateKnowledgeContentPlayableMedia.json();
    expect(updateKnowledgeContentPlayableMediaResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content podcast media should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const knowledgeContentPodcast = configuration.shareKnowledgeContent.mediaContentPodcast;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const updateKnowledgeContent = await knowledgeContentService.updateKnowledgeContent(
      knowledgeContentId,
      updateData.knowledgeContentCodeEdit,
      updateData.updateTitle,
      'video',
    );
    await expect(updateKnowledgeContent).toBeOK();
    const updateKnowledgeContentResp = await updateKnowledgeContent.json();
    expect(updateKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully updated',
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentPlayableMedia = await knowledgeContentService.updatePodcastKnowledgeContent(
      knowledgeContentId,
      knowledgeContentPodcast.media,
      knowledgeContentPodcast.mediaTranscode,
      knowledgeContentPodcast.duration,
    );
    await expect(updateKnowledgeContentPlayableMedia).not.toBeOK();
    const updateKnowledgeContentPlayableMediaResp = await updateKnowledgeContentPlayableMedia.json();
    expect(updateKnowledgeContentPlayableMediaResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content attachment should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
    mediaService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    //Upload image
    const upload = await mediaService.upload(updateData.imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(updateData.imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentAttachment = await knowledgeContentService.updateKnowledgeContentAttachment(
      knowledgeContentId,
      mediaId,
    );
    await expect(updateKnowledgeContentAttachment).not.toBeOK();
    const updateKnowledgeContentAttachmentResp = await updateKnowledgeContentAttachment.json();
    expect(updateKnowledgeContentAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content category should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const categoryIds = ['e403e01a-10e8-447f-bf5e-c381e56edea1'];

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentCategory = await knowledgeContentService.updateKnowledgeContentCategory(
      knowledgeContentId,
      categoryIds,
    );
    await expect(updateKnowledgeContentCategory).not.toBeOK();
    const updateKnowledgeContentCategoryResp = await updateKnowledgeContentCategory.json();
    expect(updateKnowledgeContentCategoryResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content comment setting should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const isCommentEnabled = false;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentComment = await knowledgeContentService.updateKnowledgeContentComment(
      knowledgeContentId,
      isCommentEnabled,
    );
    await expect(updateKnowledgeContentComment).not.toBeOK();
    const updateKnowledgeContentCommentResp = await updateKnowledgeContentComment.json();
    expect(updateKnowledgeContentCommentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit knowledge content status should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const publishedStartAt = new Date();
    const publishedEndAt = new Date(new Date().setMonth(publishedStartAt.getDate() + 1));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateKnowledgeContentStatus = await knowledgeContentService.updateKnowledgeContentStatus(
      knowledgeContentId,
      KnowledgeContentStatus.publish,
      publishedEndAt,
      publishedStartAt,
    );
    await expect(updateKnowledgeContentStatus).not.toBeOK();
    const updateKnowledgeContentStatusResp = await updateKnowledgeContentStatus.json();
    expect(updateKnowledgeContentStatusResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getKnowledgeContentDetail = await knowledgeContentService.getKnowledgeContentDetail(knowledgeContentId);
    await expect(getKnowledgeContentDetail).toBeOK();
    const getKnowledgeContentDetailResp = await getKnowledgeContentDetail.json();
    expect(getKnowledgeContentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get knowledge content item successfully',
        data: expect.objectContaining({
          status: KnowledgeContentStatus.draft,
        }),
      }),
    );
  });
});
