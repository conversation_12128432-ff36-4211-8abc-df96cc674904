;import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

const knowledgeContentTitle = 'API knowledge content';
const knowledgeContentCode = 'API2025';

test.beforeEach(async ({ knowledgeContentItemsRepo }) => {
  await knowledgeContentItemsRepo.deleteKnowledgeContentItemByCode(knowledgeContentCode);
});

test.afterEach(async ({ knowledgeContentItemsRepo }) => {
  await knowledgeContentItemsRepo.deleteKnowledgeContentItemByCode(knowledgeContentCode);
});

test.describe('Positive', () => {
  test(`User with valid permission create knowledge content type article should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'article',
    );
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully created',
        data: expect.objectContaining({
          title: knowledgeContentTitle,
          code: knowledgeContentCode,
          type: 'article',
        }),
      }),
    );
  });

  test(`User with valid permission create knowledge content type podcast should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'podcast',
    );
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully created',
        data: expect.objectContaining({
          title: knowledgeContentTitle,
          code: knowledgeContentCode,
          type: 'podcast',
        }),
      }),
    );
  });

  test(`User with valid permission create knowledge content type video should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'video',
    );
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully created',
        data: expect.objectContaining({
          title: knowledgeContentTitle,
          code: knowledgeContentCode,
          type: 'video',
        }),
      }),
    );
  });

  test(`User with valid permission create knowledge content type ebook should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'ebook',
    );
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully created',
        data: expect.objectContaining({
          title: knowledgeContentTitle,
          code: knowledgeContentCode,
          type: 'ebook',
        }),
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User without valid permission create knowledge content type article should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'article',
    );
    await expect(createKnowledgeContent).not.toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission create knowledge content type podcast should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'podcast',
    );
    await expect(createKnowledgeContent).not.toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission create knowledge content type video should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'video',
    );
    await expect(createKnowledgeContent).not.toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission create knowledge content type ebook should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'ebook',
    );
    await expect(createKnowledgeContent).not.toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
