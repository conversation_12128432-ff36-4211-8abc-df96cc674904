import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

let knowledgeContentId;

test.beforeEach(
  async ({ knowledgeContentItemsRepo, configuration, authenticationService, knowledgeContentService }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const knowledgeContentTitle = 'API knowledge content';
    const knowledgeContentCode = 'API2025';

    // clean up
    await knowledgeContentItemsRepo.deleteKnowledgeContentItemByCode(knowledgeContentCode);

    //create knowledge content
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.createKnowledgeContent(
      knowledgeContentTitle,
      knowledgeContentCode,
      'article',
    );
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully created',
        data: expect.objectContaining({
          title: knowledgeContentTitle,
          code: knowledgeContentCode,
          type: 'article',
        }),
      }),
    );
    knowledgeContentId = createKnowledgeContentResp.data.id;
  },
);

test.afterEach(async ({ knowledgeContentItemsRepo }) => {
  await knowledgeContentItemsRepo.deleteKnowledgeContentItemById(knowledgeContentId);
});

test.describe('Positive', () => {
  test(`User with valid permission delete knowledge content type article should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.deleteKnowledgeContent(knowledgeContentId);
    await expect(createKnowledgeContent).toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Successfully deleted the knowledge content item',
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User without valid permission delete knowledge content type article should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createKnowledgeContent = await knowledgeContentService.deleteKnowledgeContent(knowledgeContentId);
    await expect(createKnowledgeContent).not.toBeOK();
    const createKnowledgeContentResp = await createKnowledgeContent.json();
    expect(createKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
