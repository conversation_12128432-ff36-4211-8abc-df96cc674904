import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test(`User with valid permission get list of knowledge content should return SUCCESS`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getKnowledgeContent = await knowledgeContentService.getKnowledgeContentList();
    await expect(getKnowledgeContent).toBeOK();
    const getKnowledgeContentResp = await getKnowledgeContent.json();
    expect(getKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getKnowledgeContentResp.data.total).toBeGreaterThan(0);
  });
});

test.describe('Permission', () => {
  test(`User without permission get list of knowledge content should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    knowledgeContentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getKnowledgeContent = await knowledgeContentService.getKnowledgeContentList();
    await expect(getKnowledgeContent).not.toBeOK();
    const getKnowledgeContentResp = await getKnowledgeContent.json();
    expect(getKnowledgeContentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
