import { expect } from '@playwright/test';
import { CalendarHelper } from '../../../shared/calendar/calendar-helper';
import { ExcelTestData } from '../../../shared/excel/excel-test-data';
import { test as base } from '../../fixtures/default-fixture';
import { EnrollType } from '../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let enrollment;
let items;
let deductPayload;
let cancelDeductPayload;
let approveDeductPayload;

export const test = base.extend<{
  bulkEnrollmentRegularExcel: ExcelTestData;
  bulkRoundExcel: ExcelTestData;
}>({
  bulkEnrollmentRegularExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkEnrollTrainingTLITemplate();
    use(ExcelTestData);
  },
  bulkRoundExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkRoundTemplate();
    use(ExcelTestData);
  },
});

test.beforeEach(
  async ({
    roundRepo,
    enrollmentsRepo,
    configuration,
    authenticationService,
    enrollmentService,
    jobService,
    bulkEnrollmentRegularExcel,
    bulkRoundExcel,
    roundsService,
  }) => {
    // Arrange
    const course = configuration.shareCourses.courseOIC4;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date(new Date().setDate(new Date().getDate() - 1));
    const firstRegisDate = new Date(new Date().setDate(new Date().getDate() - 5));
    const lastRegisDate = new Date(new Date().setDate(new Date().getDate() - 4));
    const effectedDate = new Date(new Date().setDate(new Date().getDate() - 20));
    const expiredDate = new Date(new Date().setDate(new Date().getDate() - 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const additionalDocument = configuration.shareDocument.enrollmentAttachmentAdditionalType;
    const deductDocument = configuration.shareDocument.deductDocument;
    const settings = {
      '1': { requiredFile: true, requiredText: false },
      '2': { requiredFile: false, requiredText: true },
      '3': { requiredFile: false, requiredText: false },
    };

    //clean course
    await roundRepo.deleteTrainingRoundByCourseId(course.courseId);
    await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);

    //Arrange document
    items = Object.entries(additionalDocument).map(([key, item]) => ({
      enrollmentAttachmentAdditionalType: {
        name: item.name,
        requiredFile: settings[key].requiredFile,
        requiredText: settings[key].requiredText,
        description: item.description,
      },
      text: item.text,
      files: item.files,
    }));

    deductPayload = {
      information: {
        salute: userTLI.salute,
        firstname: userTLI.firstname,
        lastname: userTLI.lastname,
        citizenId: userTLI.citizenId,
      },
      criteria: {
        certificateType: Object.fromEntries(
          Object.entries(deductDocument.certificateType).map(([key, item]: any) => [
            key,
            {
              checked: item.selected,
              issuedBy: userTLI.firstname,
              effectedDate: effectedDate,
              expiredDate: expiredDate,
            },
          ]),
        ),
        educateMasterDegree: {
          checked: deductDocument.educateMasterDegree.checked.true,
          universityName: deductDocument.educateMasterDegree.universityName,
          universityCode: deductDocument.educateMasterDegree.universityCode,
          effectedDate: effectedDate,
          studyMajor: deductDocument.educateMasterDegree.studyMajor,
        },
        instructor: {
          checked: deductDocument.instructor.checked.true,
          startedDate: effectedDate,
          endedDate: expiredDate,
          courseRef: deductDocument.instructor.courseRef,
          program: deductDocument.instructor.program,
          hours: deductDocument.instructor.hours,
        },
      },
      fileInfo: {
        description: 'test description',
        files: [
          {
            fileName: 'invalid-face-image.jpg',
            filePath: './shared/ekyc/invalid-face-image.jpg',
            originalFileName: 'invalid-face-image.jpg',
          },
        ],
      },
    };

    cancelDeductPayload = {
      information: {
        salute: userTLI.salute,
        firstname: userTLI.firstname,
        lastname: userTLI.lastname,
        citizenId: userTLI.citizenId,
      },
      criteria: {
        certificateType: Object.fromEntries(
          Object.entries(deductDocument.certificateType).map(([key, item]: any) => [
            key,
            {
              checked: item.selected,
              issuedBy: userTLI.firstname,
              isVerified: false,
              effectedDate: effectedDate,
              expiredDate: expiredDate,
            },
          ]),
        ),
        educateMasterDegree: {
          checked: true,
          universityName: deductDocument.educateMasterDegree.universityName,
          universityCode: deductDocument.educateMasterDegree.universityCode,
          effectedDate: effectedDate,
          isVerified: false,
          studyMajor: deductDocument.educateMasterDegree.studyMajor,
        },
        instructor: {
          checked: true,
          startedDate: effectedDate,
          endedDate: expiredDate,
          courseRef: deductDocument.instructor.courseRef,
          program: deductDocument.instructor.program,
          hours: deductDocument.instructor.hours,
          isVerified: false,
          issuedBy: null,
        },
      },
      fileInfo: {
        description: 'test description',
        files: [
          {
            fileName: 'invalid-face-image.jpg',
            filePath: './shared/ekyc/invalid-face-image.jpg',
            originalFileName: 'invalid-face-image.jpg',
          },
        ],
      },
    };

    approveDeductPayload = {
      information: {
        salute: userTLI.salute,
        firstname: userTLI.firstname,
        lastname: userTLI.lastname,
        citizenId: userTLI.citizenId,
      },
      criteria: {
        certificateType: Object.fromEntries(
          Object.entries(deductDocument.certificateType).map(([key, item]: any) => [
            key,
            {
              checked: item.selected,
              issuedBy: userTLI.firstname,
              isVerified: true,
              effectedDate: effectedDate,
              expiredDate: expiredDate,
            },
          ]),
        ),
        educateMasterDegree: {
          checked: true,
          universityName: deductDocument.educateMasterDegree.universityName,
          universityCode: deductDocument.educateMasterDegree.universityCode,
          effectedDate: effectedDate,
          isVerified: true,
          studyMajor: deductDocument.educateMasterDegree.studyMajor,
        },
        instructor: {
          checked: true,
          startedDate: effectedDate,
          endedDate: expiredDate,
          courseRef: deductDocument.instructor.courseRef,
          program: deductDocument.instructor.program,
          hours: deductDocument.instructor.hours,
          isVerified: true,
          issuedBy: null,
        },
      },
      fileInfo: {
        description: 'test description',
        files: [
          {
            fileName: 'invalid-face-image.jpg',
            filePath: './shared/ekyc/invalid-face-image.jpg',
            originalFileName: 'invalid-face-image.jpg',
          },
        ],
      },
    };

    bulkRoundExcel.addRow({
      courseCode: course.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    const roundFileBuffer = await bulkRoundExcel.writeExcelStream();

    bulkEnrollmentRegularExcel.addRow({
      username: userTLI.username,
      courseCode: course.courseCode,
      refCode: course.refCode,
      enroll_type: EnrollType.voluntary,
    });
    const fileBuffer = await bulkEnrollmentRegularExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(roundFileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulk = await enrollmentService.createBulk(fileBuffer, roundDate);
    await expect(createBulk).toBeOK();
    const createBulkResp = await createBulk.json();
    expect(createBulkResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
      }),
    );
    await jobService.ensureJobStatusNotPending(createBulkResp.data.guid);
    await jobService.ensureJobStatusIsCompleted(createBulkResp.data.guid);
    enrollment = await enrollmentsRepo.getEnrollmentByUserId(userTLI.userId);
  },
);

test.afterEach(async ({ roundRepo, enrollmentsRepo, configuration }) => {
  const course = configuration.shareCourses.courseOIC4;
  const userTLI = configuration.usersLocal.userTLIAPI;
  await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);
  await roundRepo.deleteTrainingRoundByCourseId(course.courseId);
});

test.describe('Positive', () => {
  test(`User cancel request additional document with valid permission should return SUCCESS`, async ({
    documentService,
    enrollmentService,
  }) => {
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const cancelReason = 'Cancel test';
    const reason = 'API request additional document';
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const expiredAtString = expiredAt.toISOString();

    const requestAdditionalDocument = await enrollmentService.requestAdditionalDocument(
      enrollment.id,
      reason,
      expiredAtString,
      items,
    );
    await expect(requestAdditionalDocument).toBeOK();
    const requestAdditionalDocumentResp = await requestAdditionalDocument.json();
    expect(requestAdditionalDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success to request additional attachment.',
        domain: 'enrollment-attachment',
      }),
    );

    const getEnrollmentAttachments = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAttachments).toBeOK();
    const getEnrollmentAttachmentsResp = await getEnrollmentAttachments.json();
    expect(getEnrollmentAttachmentsResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentAttachmentsResp.data[0].id;

    const cancelRequestAttachment = await documentService.cancelRequestAttachment(attachmentId, cancelReason);
    await expect(cancelRequestAttachment).toBeOK();
    const cancelRequestAttachmentResp = await cancelRequestAttachment.json();
    expect(cancelRequestAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        domain: 'enrollment-attachment',
      }),
    );

    const getAttachmentsDetail = await documentService.getAttachmentAdditionalDetail(attachmentId);
    await expect(getAttachmentsDetail).toBeOK();
    const getAttachmentsDetailResp = await getAttachmentsDetail.json();
    expect(getAttachmentsDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          status: 'CANCELED',
          payload: expect.objectContaining({
            items: items,
          }),
        }),
      }),
    );
  });

  test(`User cancel deduct document with valid permission should return SUCCESS`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reason = 'Cancel test';

    const getEnrollmentDeductAttachmentDetail = await enrollmentService.getEnrollmentDeductAttachmentDetail(
      enrollment.id,
    );
    await expect(getEnrollmentDeductAttachmentDetail).toBeOK();
    const getEnrollmentDeductAttachmentDetailResp = await getEnrollmentDeductAttachmentDetail.json();
    expect(getEnrollmentDeductAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentDeductAttachmentDetailResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getRequestAttachmentDetail = await documentService.getRequestAttachmentDetail(
      enrollment.id,
      attachmentId,
      'deduct',
    );
    await expect(getRequestAttachmentDetail).toBeOK();
    const getRequestAttachmentDetailResp = await getRequestAttachmentDetail.json();
    expect(getRequestAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        data: expect.objectContaining({
          enrollmentAttachment: expect.objectContaining({
            enrollmentId: enrollment.id,
            id: attachmentId,
            fileType: 'deduct',
            status: 'NOT_SENT',
          }),
        }),
      }),
    );

    const sendDeductDocument = await documentService.sendDeductDocument(enrollment.id, attachmentId, deductPayload);
    await expect(sendDeductDocument).toBeOK();
    const sendDeductDocumentResp = await sendDeductDocument.json();
    expect(sendDeductDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        domain: 'enrollment',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAttachmentsDetail = await documentService.getAttachmentDeductDetail(attachmentId);
    await expect(getAttachmentsDetail).toBeOK();
    const getAttachmentsDetailResp = await getAttachmentsDetail.json();
    expect(getAttachmentsDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          fileType: 'deduct',
          status: 'PENDING_APPROVAL',
        }),
      }),
    );

    const cancelDeductAttachment = await documentService.cancelDeductAttachment(
      attachmentId,
      cancelDeductPayload,
      reason,
    );
    await expect(cancelDeductAttachment).toBeOK();
    const cancelDeductAttachmentResp = await cancelDeductAttachment.json();
    expect(cancelDeductAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
      }),
    );
  });

  test(`User approve deduct document with valid permission should return SUCCESS`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reason = 'Approved';

    const getEnrollmentDeductAttachmentDetail = await enrollmentService.getEnrollmentDeductAttachmentDetail(
      enrollment.id,
    );
    await expect(getEnrollmentDeductAttachmentDetail).toBeOK();
    const getEnrollmentDeductAttachmentDetailResp = await getEnrollmentDeductAttachmentDetail.json();
    expect(getEnrollmentDeductAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentDeductAttachmentDetailResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const sendDeductDocument = await documentService.sendDeductDocument(enrollment.id, attachmentId, deductPayload);
    await expect(sendDeductDocument).toBeOK();
    const sendDeductDocumentResp = await sendDeductDocument.json();
    expect(sendDeductDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        domain: 'enrollment',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAttachmentsDetail = await documentService.getAttachmentDeductDetail(attachmentId);
    await expect(getAttachmentsDetail).toBeOK();
    const getAttachmentsDetailResp = await getAttachmentsDetail.json();
    expect(getAttachmentsDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          fileType: 'deduct',
          status: 'PENDING_APPROVAL',
        }),
      }),
    );

    const approveDeductAttachment = await documentService.approveDeductAttachment(
      attachmentId,
      approveDeductPayload,
      reason,
    );
    await expect(approveDeductAttachment).toBeOK();
    const approveDeductAttachmentResp = await approveDeductAttachment.json();
    expect(approveDeductAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User cancel request additional document without valid permission should return Forbidden`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const cancelReason = 'Cancel test';
    const reason = 'API request additional document';
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const expiredAtString = expiredAt.toISOString();

    const requestAdditionalDocument = await enrollmentService.requestAdditionalDocument(
      enrollment.id,
      reason,
      expiredAtString,
      items,
    );
    await expect(requestAdditionalDocument).toBeOK();
    const requestAdditionalDocumentResp = await requestAdditionalDocument.json();
    expect(requestAdditionalDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success to request additional attachment.',
        domain: 'enrollment-attachment',
      }),
    );

    const getEnrollmentAttachments = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAttachments).toBeOK();
    const getEnrollmentAttachmentsResp = await getEnrollmentAttachments.json();
    expect(getEnrollmentAttachmentsResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentAttachmentsResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const cancelRequestAttachment = await documentService.cancelRequestAttachment(attachmentId, cancelReason);
    await expect(cancelRequestAttachment).not.toBeOK();
    const cancelRequestAttachmentResp = await cancelRequestAttachment.json();
    expect(cancelRequestAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAttachmentsDetail = await documentService.getAttachmentAdditionalDetail(attachmentId);
    await expect(getAttachmentsDetail).toBeOK();
    const getAttachmentsDetailResp = await getAttachmentsDetail.json();
    expect(getAttachmentsDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          status: 'NOT_SENT',
          payload: expect.objectContaining({
            items: items,
          }),
        }),
      }),
    );
  });

  test(`User cancel deduct document without valid permission should return Forbidden`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reason = 'Cancel test';

    const getEnrollmentDeductAttachmentDetail = await enrollmentService.getEnrollmentDeductAttachmentDetail(
      enrollment.id,
    );
    await expect(getEnrollmentDeductAttachmentDetail).toBeOK();
    const getEnrollmentDeductAttachmentDetailResp = await getEnrollmentDeductAttachmentDetail.json();
    expect(getEnrollmentDeductAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentDeductAttachmentDetailResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const sendDeductDocument = await documentService.sendDeductDocument(enrollment.id, attachmentId, deductPayload);
    await expect(sendDeductDocument).toBeOK();
    const sendDeductDocumentResp = await sendDeductDocument.json();
    expect(sendDeductDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        domain: 'enrollment',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAttachmentsDetail = await documentService.getAttachmentDeductDetail(attachmentId);
    await expect(getAttachmentsDetail).toBeOK();
    const getAttachmentsDetailResp = await getAttachmentsDetail.json();
    expect(getAttachmentsDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          fileType: 'deduct',
          status: 'PENDING_APPROVAL',
        }),
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const cancelDeductAttachment = await documentService.cancelDeductAttachment(
      attachmentId,
      cancelDeductPayload,
      reason,
    );
    await expect(cancelDeductAttachment).not.toBeOK();
    const cancelDeductAttachmentResp = await cancelDeductAttachment.json();
    expect(cancelDeductAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User approve deduct document without valid permission should return Forbidden`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reason = 'Approved';

    const getEnrollmentDeductAttachmentDetail = await enrollmentService.getEnrollmentDeductAttachmentDetail(
      enrollment.id,
    );
    await expect(getEnrollmentDeductAttachmentDetail).toBeOK();
    const getEnrollmentDeductAttachmentDetailResp = await getEnrollmentDeductAttachmentDetail.json();
    expect(getEnrollmentDeductAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentDeductAttachmentDetailResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const sendDeductDocument = await documentService.sendDeductDocument(enrollment.id, attachmentId, deductPayload);
    await expect(sendDeductDocument).toBeOK();
    const sendDeductDocumentResp = await sendDeductDocument.json();
    expect(sendDeductDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        domain: 'enrollment',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAttachmentsDetail = await documentService.getAttachmentDeductDetail(attachmentId);
    await expect(getAttachmentsDetail).toBeOK();
    const getAttachmentsDetailResp = await getAttachmentsDetail.json();
    expect(getAttachmentsDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          fileType: 'deduct',
          status: 'PENDING_APPROVAL',
        }),
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const approveDeductAttachment = await documentService.approveDeductAttachment(
      attachmentId,
      approveDeductPayload,
      reason,
    );
    await expect(approveDeductAttachment).not.toBeOK();
    const approveDeductAttachmentResp = await approveDeductAttachment.json();
    expect(approveDeductAttachmentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
