import { expect } from '@playwright/test';
import { CalendarHelper } from '../../../shared/calendar/calendar-helper';
import { ExcelTestData } from '../../../shared/excel/excel-test-data';
import { test as base } from '../../fixtures/default-fixture';
import { EnrollType } from '../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let enrollment;
let items;

export const test = base.extend<{
  bulkEnrollmentRegularExcel: ExcelTestData;
  bulkRoundExcel: ExcelTestData;
}>({
  bulkEnrollmentRegularExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkEnrollTrainingTLITemplate();
    use(ExcelTestData);
  },
  bulkRoundExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkRoundTemplate();
    use(ExcelTestData);
  },
});

test.beforeEach(
  async ({
    roundRepo,
    enrollmentsRepo,
    configuration,
    authenticationService,
    enrollmentService,
    bulkEnrollmentRegularExcel,
    jobService,
    bulkRoundExcel,
    roundsService,
  }) => {
    // Arrange
    const course = configuration.shareCourses.courseOIC4;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    //clean course
    await roundRepo.deleteTrainingRoundByCourseId(course.courseId);
    await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setDate(new Date().getDate() - 5));
    const lastRegisDate = new Date(new Date().setDate(new Date().getDate() - 4));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    bulkRoundExcel.addRow({
      courseCode: course.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    const roundFileBuffer = await bulkRoundExcel.writeExcelStream();

    bulkEnrollmentRegularExcel.addRow({
      username: userTLI.username,
      courseCode: course.courseCode,
      refCode: course.refCode,
      enroll_type: EnrollType.voluntary,
    });
    const fileBuffer = await bulkEnrollmentRegularExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(roundFileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulk = await enrollmentService.createBulk(fileBuffer, date);
    await expect(createBulk).toBeOK();
    const createBulkResp = await createBulk.json();
    expect(createBulkResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
      }),
    );
    await jobService.ensureJobStatusNotPending(createBulkResp.data.guid);
    await jobService.ensureJobStatusIsCompleted(createBulkResp.data.guid);
    enrollment = await enrollmentsRepo.getEnrollmentByUserId(userTLI.userId);

    const additionalDocument = configuration.shareDocument.enrollmentAttachmentAdditionalType;
    const reason = 'API request additional document';
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const expiredAtString = expiredAt.toISOString();
    const settings = {
      '1': { requiredFile: true, requiredText: false },
      '2': { requiredFile: false, requiredText: true },
      '3': { requiredFile: false, requiredText: false },
    };

    items = Object.entries(additionalDocument).map(([key, item]) => ({
      enrollmentAttachmentAdditionalType: {
        name: item.name,
        requiredFile: settings[key].requiredFile,
        requiredText: settings[key].requiredText,
        description: item.description,
      },
      text: item.text,
      files: item.files,
    }));

    const requestAdditionalDocument = await enrollmentService.requestAdditionalDocument(
      enrollment.id,
      reason,
      expiredAtString,
      items,
    );
    await expect(requestAdditionalDocument).toBeOK();
    const requestAdditionalDocumentResp = await requestAdditionalDocument.json();
    expect(requestAdditionalDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success to request additional attachment.',
        domain: 'enrollment-attachment',
      }),
    );
  },
);

test.afterEach(async ({ roundRepo, enrollmentsRepo, configuration }) => {
  const course = configuration.shareCourses.courseOIC4;
  const userTLI = configuration.usersLocal.userTLIAPI;
  await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);
  await roundRepo.deleteTrainingRoundByCourseId(course.courseId);
});

test.describe('Positive', () => {
  test(`User get additional document list with valid permission should return SUCCESS`, async ({
    documentService,
    configuration,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const course = configuration.shareCourses.courseOIC4;
    const status = 'NOT_SENT';
    const roundDate = new Date();
    const startDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const endDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));

    const getEnrollmentAdditionalAttachmentsList = await documentService.getEnrollmentAdditionalAttachmentsList(
      userTLI.firstname,
      userTLI.email,
      course.courseVersions[1].name,
      status,
      startDate,
      endDate,
    );
    await expect(getEnrollmentAdditionalAttachmentsList).toBeOK();
    const getEnrollmentAdditionalAttachmentsListResp = await getEnrollmentAdditionalAttachmentsList.json();
    expect(getEnrollmentAdditionalAttachmentsListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getEnrollmentAdditionalAttachmentsListResp.data.total).toBeGreaterThan(0);
  });

  test(`User get deduct document list with valid permission should return SUCCESS`, async ({
    documentService,
    configuration,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const course = configuration.shareCourses.courseOIC4;
    const status = 'NOT_SENT';
    const roundDate = new Date();
    const startDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const endDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));

    const getEnrollmentDeductAttachmentsList = await documentService.getEnrollmentDeductAttachmentsList(
      userTLI.firstname,
      userTLI.email,
      userTLI.citizenId,
      course.courseVersions[1].name,
      status,
      startDate,
      endDate,
    );
    await expect(getEnrollmentDeductAttachmentsList).toBeOK();
    const getEnrollmentDeductAttachmentsListResp = await getEnrollmentDeductAttachmentsList.json();
    expect(getEnrollmentDeductAttachmentsListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getEnrollmentDeductAttachmentsListResp.data.total).toBeGreaterThan(0);
  });

  test(`User get enrollment additional document detail with valid permission should return SUCCESS`, async ({
    enrollmentService,
  }) => {
    const reason = 'API request additional document';
    const getEnrollmentAdditionalAttachmentDetail = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAdditionalAttachmentDetail).toBeOK();
    const getEnrollmentAdditionalAttachmentDetailResp = await getEnrollmentAdditionalAttachmentDetail.json();
    expect(getEnrollmentAdditionalAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
            status: 'NOT_SENT',
            payload: expect.objectContaining({
              reason: reason,
              items: items,
            }),
          }),
        ]),
      }),
    );
  });

  test(`User get additional document detail with valid permission should return SUCCESS`, async ({
    documentService,
    enrollmentService,
  }) => {
    const getEnrollmentAdditionalAttachmentDetail = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAdditionalAttachmentDetail).toBeOK();
    const getEnrollmentAdditionalAttachmentDetailResp = await getEnrollmentAdditionalAttachmentDetail.json();
    expect(getEnrollmentAdditionalAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentAdditionalAttachmentDetailResp.data[0].id;

    const getAttachmentAdditionalDetail = await documentService.getAttachmentAdditionalDetail(attachmentId);
    await expect(getAttachmentAdditionalDetail).toBeOK();
    const getAttachmentAdditionalDetailResp = await getAttachmentAdditionalDetail.json();
    expect(getAttachmentAdditionalDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          status: 'NOT_SENT',
          payload: expect.objectContaining({
            items: items,
          }),
          fileType: 'additional',
        }),
      }),
    );
  });

  test(`User get deduct document detail with valid permission should return SUCCESS`, async ({
    documentService,
    enrollmentService,
  }) => {
    const getEnrollmentDeductAttachmentDetail = await enrollmentService.getEnrollmentDeductAttachmentDetail(enrollment.id);
    await expect(getEnrollmentDeductAttachmentDetail).toBeOK();
    const getEnrollmentDeductAttachmentDetailResp = await getEnrollmentDeductAttachmentDetail.json();
    expect(getEnrollmentDeductAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentDeductAttachmentDetailResp.data[0].id;

    const getAttachmentDeductDetail = await documentService.getAttachmentDeductDetail(attachmentId);
    await expect(getAttachmentDeductDetail).toBeOK();
    const getAttachmentDeductDetailResp = await getAttachmentDeductDetail.json();
    expect(getAttachmentDeductDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          id: attachmentId,
          enrollmentId: enrollment.id,
          status: 'NOT_SENT',
          fileType: 'deduct',
        }),
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User get additional document list without valid permission should return Forbidden`, async ({
    documentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const course = configuration.shareCourses.courseOIC4;
    const status = 'NOT_SENT';
    const roundDate = new Date();
    const startDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const endDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getEnrollmentAdditionalAttachmentsList = await documentService.getEnrollmentAdditionalAttachmentsList(
      userTLI.firstname,
      userTLI.email,
      course.courseVersions[1].name,
      status,
      startDate,
      endDate,
    );
    await expect(getEnrollmentAdditionalAttachmentsList).not.toBeOK();
    const getEnrollmentAdditionalAttachmentsListResp = await getEnrollmentAdditionalAttachmentsList.json();
    expect(getEnrollmentAdditionalAttachmentsListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User get deduct document list without valid permission should return Forbidden`, async ({
    documentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const course = configuration.shareCourses.courseOIC4;
    const status = 'NOT_SENT';
    const roundDate = new Date();
    const startDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const endDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getEnrollmentDeductAttachmentsList = await documentService.getEnrollmentDeductAttachmentsList(
      userTLI.firstname,
      userTLI.email,
      userTLI.citizenId,
      course.courseVersions[1].name,
      status,
      startDate,
      endDate,
    );
    await expect(getEnrollmentDeductAttachmentsList).not.toBeOK();
    const getEnrollmentDeductAttachmentsListResp = await getEnrollmentDeductAttachmentsList.json();
    expect(getEnrollmentDeductAttachmentsListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User get enrollment additional document detail without valid permission should return Forbidden`, async ({
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getEnrollmentAdditionalAttachmentDetail = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAdditionalAttachmentDetail).not.toBeOK();
    const getEnrollmentAdditionalAttachmentDetailResp = await getEnrollmentAdditionalAttachmentDetail.json();
    expect(getEnrollmentAdditionalAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User get additional document detail without valid permission should return Forbidden`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    const getEnrollmentAdditionalAttachmentDetail = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAdditionalAttachmentDetail).toBeOK();
    const getEnrollmentAdditionalAttachmentDetailResp = await getEnrollmentAdditionalAttachmentDetail.json();
    expect(getEnrollmentAdditionalAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentAdditionalAttachmentDetailResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getAttachmentAdditionalDetail = await documentService.getAttachmentAdditionalDetail(attachmentId);
    await expect(getAttachmentAdditionalDetail).not.toBeOK();
    const getAttachmentAdditionalDetailResp = await getAttachmentAdditionalDetail.json();
    expect(getAttachmentAdditionalDetailResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User get deduct document detail without valid permission should return Forbidden`, async ({
    documentService,
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    const getEnrollmentDeductAttachmentDetail = await enrollmentService.getEnrollmentDeductAttachmentDetail(enrollment.id);
    await expect(getEnrollmentDeductAttachmentDetail).toBeOK();
    const getEnrollmentDeductAttachmentDetailResp = await getEnrollmentDeductAttachmentDetail.json();
    expect(getEnrollmentDeductAttachmentDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
          }),
        ]),
      }),
    );
    const attachmentId = getEnrollmentDeductAttachmentDetailResp.data[0].id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getAttachmentDeductDetail = await documentService.getAttachmentDeductDetail(attachmentId);
    await expect(getAttachmentDeductDetail).not.toBeOK();
    const getAttachmentDeductDetailResp = await getAttachmentDeductDetail.json();
    expect(getAttachmentDeductDetailResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
