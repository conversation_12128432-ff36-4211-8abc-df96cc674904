import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { ExcelTestData } from '../../../shared/excel/excel-test-data';
import { test as base } from '../../fixtures/default-fixture';
import { EnrollType } from '../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let enrollment;

export const test = base.extend<{
  bulkEnrollmentRegularExcel: ExcelTestData;
}>({
  bulkEnrollmentRegularExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkEnrollTrainingTLITemplate();
    use(ExcelTestData);
  },
});

test.beforeEach(
  async ({
    roundRepo,
    enrollmentsRepo,
    configuration,
    authenticationService,
    jobService,
    bulkEnrollmentRegularExcel,
    enrollmentService,
  }) => {
    // Arrange
    const course = configuration.shareCourses.courseOIC4;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    //clean course
    await roundRepo.deleteTrainingRoundByCourseId(course.courseId);
    await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    // Seed round
    await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    bulkEnrollmentRegularExcel.addRow({
      username: userTLI.username,
      courseCode: course.courseCode,
      refCode: course.refCode,
      enroll_type: EnrollType.voluntary,
    });
    const fileBuffer = await bulkEnrollmentRegularExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createBulk = await enrollmentService.createBulk(fileBuffer, date);
    await expect(createBulk).toBeOK();
    const createBulkResp = await createBulk.json();
    expect(createBulkResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
      }),
    );
    await jobService.ensureJobStatusNotPending(createBulkResp.data.guid);
    await jobService.ensureJobStatusIsCompleted(createBulkResp.data.guid);
    enrollment = await enrollmentsRepo.getEnrollmentByUserId(userTLI.userId);
  },
);

test.describe('Positive', () => {
  test(`User request additional document with valid permission remark should return SUCCESS`, async ({
    enrollmentService,
    configuration,
  }) => {
    const additionalDocument = configuration.shareDocument.enrollmentAttachmentAdditionalType;
    const reason = 'API request additional document';
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const expiredAtString = expiredAt.toISOString();
    const settings = {
      '1': { requiredFile: true, requiredText: false },
      '2': { requiredFile: false, requiredText: true },
      '3': { requiredFile: false, requiredText: false },
    };

    const items = Object.entries(additionalDocument).map(([key, item]) => ({
      enrollmentAttachmentAdditionalType: {
        name: item.name,
        requiredFile: settings[key].requiredFile,
        requiredText: settings[key].requiredText,
        description: item.description,
      },
      text: item.text,
      files: item.files,
    }));

    const requestAdditionalDocument = await enrollmentService.requestAdditionalDocument(
      enrollment.id,
      reason,
      expiredAtString,
      items,
    );
    await expect(requestAdditionalDocument).toBeOK();
    const requestAdditionalDocumentResp = await requestAdditionalDocument.json();
    expect(requestAdditionalDocumentResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success to request additional attachment.',
        domain: 'enrollment-attachment',
      }),
    );
    const getEnrollmentAttachments = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAttachments).toBeOK();
    const getEnrollmentAttachmentsResp = await getEnrollmentAttachments.json();
    expect(getEnrollmentAttachmentsResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.arrayContaining([
          expect.objectContaining({
            enrollmentId: enrollment.id,
            status: 'NOT_SENT',
            payload: expect.objectContaining({
              reason: reason,
              items: items,
            }),
          }),
        ]),
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User request additional document without valid permission should return Forbidden`, async ({
    enrollmentService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const additionalDocument = configuration.shareDocument.enrollmentAttachmentAdditionalType;
    const reason = 'API request additional document';
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const expiredAtString = expiredAt.toISOString();
    const settings = {
      '1': { requiredFile: true, requiredText: false },
      '2': { requiredFile: false, requiredText: true },
      '3': { requiredFile: false, requiredText: false },
    };

    const items = Object.entries(additionalDocument).map(([key, item]) => ({
      enrollmentAttachmentAdditionalType: {
        name: item.name,
        requiredFile: settings[key].requiredFile,
        requiredText: settings[key].requiredText,
        description: item.description,
      },
      text: item.text,
      files: item.files,
    }));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const requestAdditionalDocument = await enrollmentService.requestAdditionalDocument(
      enrollment.id,
      reason,
      expiredAtString,
      items,
    );
    await expect(requestAdditionalDocument).not.toBeOK();
    const requestAdditionalDocumentResp = await requestAdditionalDocument.json();
    expect(requestAdditionalDocumentResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );

    const adminTLI = configuration.usersLocal.adminTLIAPI;
    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getEnrollmentAttachments = await enrollmentService.getEnrollmentAdditionalAttachmentDetail(enrollment.id);
    await expect(getEnrollmentAttachments).toBeOK();
    const getEnrollmentAttachmentsResp = await getEnrollmentAttachments.json();
    expect(getEnrollmentAttachmentsResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: [],
      }),
    );
  });
});
