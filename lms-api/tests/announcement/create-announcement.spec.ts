import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test(`User create announcement with valid permission should return SUCCESS`, async ({
    announcementService,
    configuration,
    announcementRepo,
    authenticationService,
  }) => {
    const title = 'API announcement title';
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createAnnouncement = await announcementService.createAnnouncement(title);
    await expect(createAnnouncement).toBeOK();
    const createAnnouncementResp = await createAnnouncement.json();
    expect(createAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Create announcement successfully',
        data: expect.objectContaining({
          title: title,
          status: `DRAFT`,
        }),
      }),
    );
    await announcementRepo.deleteAnnouncementById(createAnnouncementResp.data.id);
  });
});

test.describe('Permission', () => {
  test(`User without valid permission create announcement should return FORBIDDEN`, async ({
    announcementService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const title = 'API announcement title';

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createAnnouncement = await announcementService.createAnnouncement(title);
    await expect(createAnnouncement).not.toBeOK();
    const createAnnouncementResp = await createAnnouncement.json();
    expect(createAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
