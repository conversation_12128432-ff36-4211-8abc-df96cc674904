import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';
import { AnnouncementStatus } from '../../../shared/repositories/lms/constants/enums/announcement.enum';

let announcementId;
let mediaId;

test.beforeEach(async ({ configuration, authenticationService, announcementService }) => {
  const adminTLI = configuration.usersLocal.adminTLIAPI;
  const title = 'API announcement title';
  await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

  const createAnnouncement = await announcementService.createAnnouncement(title);
  await expect(createAnnouncement).toBeOK();
  const createAnnouncementResp = await createAnnouncement.json();
  expect(createAnnouncementResp).toEqual(
    expect.objectContaining({
      status: 'SUCCESS',
      message: 'Create announcement successfully',
      data: expect.objectContaining({
        title: title,
        status: `DRAFT`,
      }),
    }),
  );

  announcementId = createAnnouncementResp.data.id;
});

test.afterEach(async ({ announcementRepo, mediaRepo }) => {
  await mediaRepo.deleteById(mediaId);
  await announcementRepo.deleteAnnouncementById(announcementId);
});

test.describe('Positive', () => {
  test(`User with valid permission edit announcement should return SUCCESS`, async ({
    announcementService,
    mediaService,
  }) => {
    const imagePath = './shared/ekyc/valid-id-card.jpg';
    const authors = 'authors';
    const updateTitle = 'updateTitle';
    const contentHtml = 'PHAgdGV4dGFsaWduPSJsZWZ0Ij5lZGl0IGRldGFpbDwvcD4=';

    //Upload image
    const upload = await mediaService.upload(imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    const updateAnnouncement = await announcementService.updateAnnouncement(
      announcementId,
      authors,
      updateTitle,
      contentHtml,
      mediaId,
    );
    await expect(updateAnnouncement).toBeOK();
    const updateAnnouncementResp = await updateAnnouncement.json();
    expect(updateAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update announcement status successfully',
      }),
    );

    const getAnnouncementDetail = await announcementService.getAnnouncementDetail(announcementId);
    await expect(getAnnouncementDetail).toBeOK();
    const getAnnouncementDetailResp = await getAnnouncementDetail.json();
    expect(getAnnouncementDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get announcement successfully',
        data: expect.objectContaining({
          title: updateTitle,
          thumbnailMediaId: mediaId,
          authors: [authors],
        }),
      }),
    );
  });

  test(`User with valid permission edit announcement attachment should return SUCCESS`, async ({
    announcementService,
    mediaService,
  }) => {
    const imagePath = './shared/ekyc/valid-id-card.jpg';

    //Upload image
    const upload = await mediaService.upload(imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    const updateAnnouncement = await announcementService.updateAnnouncementAttachment(announcementId, mediaId);
    await expect(updateAnnouncement).toBeOK();
    const updateAnnouncementResp = await updateAnnouncement.json();
    expect(updateAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update announcement attachments successfully',
      }),
    );

    const getAnnouncementDetail = await announcementService.getAnnouncementDetail(announcementId);
    await expect(getAnnouncementDetail).toBeOK();
    const getAnnouncementDetailResp = await getAnnouncementDetail.json();
    expect(getAnnouncementDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get announcement successfully',
        data: expect.objectContaining({
          attachments: expect.arrayContaining([
            expect.objectContaining({
              id: mediaId,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission edit announcement status should return SUCCESS`, async ({ announcementService }) => {
    const publishedStartAt = new Date();
    const publishedEndAt = new Date(new Date().setMonth(publishedStartAt.getDate() + 1));

    const updateAnnouncementStatus = await announcementService.updateAnnouncementStatus(
      announcementId,
      AnnouncementStatus.publish,
      publishedEndAt,
      publishedStartAt,
    );
    await expect(updateAnnouncementStatus).toBeOK();
    const updateAnnouncementStatusResp = await updateAnnouncementStatus.json();
    expect(updateAnnouncementStatusResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update announcement status successfully',
      }),
    );

    const getAnnouncementDetail = await announcementService.getAnnouncementDetail(announcementId);
    await expect(getAnnouncementDetail).toBeOK();
    const getAnnouncementDetailResp = await getAnnouncementDetail.json();
    expect(getAnnouncementDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get announcement successfully',
        data: expect.objectContaining({
          status: 'PUBLISHED',
        }),
      }),
    );
  });

  test(`User with valid permission edit announcement highlight should return SUCCESS`, async ({
    announcementService,
  }) => {
    const publishedStartAt = new Date();
    const publishedEndAt = new Date(new Date().setMonth(publishedStartAt.getDate() + 1));

    const updateAnnouncementStatus = await announcementService.updateAnnouncementStatus(
      announcementId,
      AnnouncementStatus.publish,
      publishedEndAt,
      publishedStartAt,
    );
    await expect(updateAnnouncementStatus).toBeOK();
    const updateAnnouncementStatusResp = await updateAnnouncementStatus.json();
    expect(updateAnnouncementStatusResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update announcement status successfully',
      }),
    );

    const getAnnouncementDetail = await announcementService.getAnnouncementDetail(announcementId);
    await expect(getAnnouncementDetail).toBeOK();
    const getAnnouncementDetailResp = await getAnnouncementDetail.json();
    expect(getAnnouncementDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get announcement successfully',
        data: expect.objectContaining({
          status: 'PUBLISHED',
        }),
      }),
    );

    const createAnnouncementHighlight = await announcementService.createAnnouncementHighlight([
      { id: announcementId, highlightIndex: 0 },
    ]);
    await expect(createAnnouncementHighlight).toBeOK();
    const createAnnouncementHighlightResp = await createAnnouncementHighlight.json();
    expect(createAnnouncementHighlightResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update highlight announcements successfully',
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User without valid permission edit announcement should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    announcementService,
    mediaService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const imagePath = './shared/ekyc/valid-id-card.jpg';
    const authors = 'authors';
    const updateTitle = 'updateTitle';
    const contentHtml = 'PHAgdGV4dGFsaWduPSJsZWZ0Ij5lZGl0IGRldGFpbDwvcD4=';

    //Upload image
    const upload = await mediaService.upload(imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateAnnouncement = await announcementService.updateAnnouncement(
      announcementId,
      authors,
      updateTitle,
      contentHtml,
      mediaId,
    );
    await expect(updateAnnouncement).not.toBeOK();
    const updateAnnouncementResp = await updateAnnouncement.json();
    expect(updateAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit announcement attachment should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    announcementService,
    mediaService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const imagePath = './shared/ekyc/valid-id-card.jpg';

    //Upload image
    const upload = await mediaService.upload(imagePath);
    await expect(upload).toBeOK();

    const createMedia = await mediaService.createMedia(imagePath);
    await expect(createMedia).toBeOK();
    const createMediaResp = await createMedia.json();
    mediaId = createMediaResp.data.media.id;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateAnnouncement = await announcementService.updateAnnouncementAttachment(announcementId, mediaId);
    await expect(updateAnnouncement).not.toBeOK();
    const updateAnnouncementResp = await updateAnnouncement.json();
    expect(updateAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission edit announcement status should return FORBIDDEN`, async ({
    announcementService,
    configuration,
    authenticationService,
  }) => {
    const publishedStartAt = new Date();
    const publishedEndAt = new Date(new Date().setMonth(publishedStartAt.getDate() + 1));
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const updateAnnouncementStatus = await announcementService.updateAnnouncementStatus(
      announcementId,
      AnnouncementStatus.publish,
      publishedEndAt,
      publishedStartAt,
    );
    await expect(updateAnnouncementStatus).not.toBeOK();
    const updateAnnouncementStatusResp = await updateAnnouncementStatus.json();
    expect(updateAnnouncementStatusResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAnnouncementDetail = await announcementService.getAnnouncementDetail(announcementId);
    await expect(getAnnouncementDetail).toBeOK();
    const getAnnouncementDetailResp = await getAnnouncementDetail.json();
    expect(getAnnouncementDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get announcement successfully',
        data: expect.objectContaining({
          status: 'DRAFT',
        }),
      }),
    );
  });

  test(`User without valid permission edit announcement highlight should return FORBIDDEN`, async ({
    announcementService,
    configuration,
    authenticationService,
  }) => {
    const publishedStartAt = new Date();
    const publishedEndAt = new Date(new Date().setMonth(publishedStartAt.getDate() + 1));
    const userTLI = configuration.usersLocal.userTLIAPI;

    const updateAnnouncementStatus = await announcementService.updateAnnouncementStatus(
      announcementId,
      AnnouncementStatus.publish,
      publishedEndAt,
      publishedStartAt,
    );
    await expect(updateAnnouncementStatus).toBeOK();
    const updateAnnouncementStatusResp = await updateAnnouncementStatus.json();
    expect(updateAnnouncementStatusResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Update announcement status successfully',
      }),
    );

    const getAnnouncementDetail = await announcementService.getAnnouncementDetail(announcementId);
    await expect(getAnnouncementDetail).toBeOK();
    const getAnnouncementDetailResp = await getAnnouncementDetail.json();
    expect(getAnnouncementDetailResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get announcement successfully',
        data: expect.objectContaining({
          status: 'PUBLISHED',
        }),
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createAnnouncementHighlight = await announcementService.createAnnouncementHighlight([
      { id: announcementId, highlightIndex: 0 },
    ]);
    await expect(createAnnouncementHighlight).not.toBeOK();
    const createAnnouncementHighlightResp = await createAnnouncementHighlight.json();
    expect(createAnnouncementHighlightResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
