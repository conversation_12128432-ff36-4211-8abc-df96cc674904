import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

let announcementId;

test.beforeEach(async ({ configuration, authenticationService, announcementService }) => {

  const adminTLI = configuration.usersLocal.adminTLIAPI;
  const title = 'API announcement title';
  await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

  const createAnnouncement = await announcementService.createAnnouncement(title);
  await expect(createAnnouncement).toBeOK();
  const createAnnouncementResp = await createAnnouncement.json();
  expect(createAnnouncementResp).toEqual(
    expect.objectContaining({
      status: 'SUCCESS',
      message: 'Create announcement successfully',
      data: expect.objectContaining({
        title: title,
        status: `DRAFT`,
      }),
    }),
  );

  announcementId = createAnnouncementResp.data.id;
});

test.afterEach(async ({ announcementRepo }) => {
  await announcementRepo.deleteAnnouncementById(announcementId);
});

test.describe('Positive', () => {
  test(`User with valid permission get list of announcement should return SUCCESS`, async ({
    configuration,
    authenticationService,
    announcementService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getAnnouncementList = await announcementService.getAnnouncementList();
    await expect(getAnnouncementList).toBeOK();
    const getAnnouncementListResp = await getAnnouncementList.json();
    expect(getAnnouncementListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getAnnouncementListResp.data.total).toBeGreaterThan(0);
  });
});

test.describe('Permission', () => {
  test(`User without valid permission get list of announcement should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    announcementService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getAnnouncementList = await announcementService.getAnnouncementList();
    await expect(getAnnouncementList).not.toBeOK();
    const getAnnouncementListResp = await getAnnouncementList.json();
    expect(getAnnouncementListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
