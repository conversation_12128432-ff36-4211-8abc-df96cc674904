import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

let announcementId;

test.beforeEach(async ({ configuration, authenticationService, announcementService }) => {
  const adminTLI = configuration.usersLocal.adminTLIAPI;
  const title = 'API announcement title';
  await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

  const createAnnouncement = await announcementService.createAnnouncement(title);
  await expect(createAnnouncement).toBeOK();
  const createAnnouncementResp = await createAnnouncement.json();
  expect(createAnnouncementResp).toEqual(
    expect.objectContaining({
      status: 'SUCCESS',
      message: 'Create announcement successfully',
      data: expect.objectContaining({
        title: title,
        status: `DRAFT`,
      }),
    }),
  );

  announcementId = createAnnouncementResp.data.id;
});

test.afterEach(async ({ announcementRepo }) => {
  await announcementRepo.deleteAnnouncementById(announcementId);
});

test.describe('Positive', () => {
  test(`User with valid permission delete announcement should return SUCCESS`, async ({ announcementService }) => {
    const deleteAnnouncement = await announcementService.deleteAnnouncement(announcementId);
    await expect(deleteAnnouncement).toBeOK();
    const deleteAnnouncementResp = await deleteAnnouncement.json();
    expect(deleteAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Delete announcement status successfully',
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User without valid permission delete announcement should return FORBIDDEN`, async ({
    announcementService,
    configuration,
    authenticationService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const deleteAnnouncement = await announcementService.deleteAnnouncement(announcementId);
    await expect(deleteAnnouncement).not.toBeOK();
    const deleteAnnouncementResp = await deleteAnnouncement.json();
    expect(deleteAnnouncementResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
