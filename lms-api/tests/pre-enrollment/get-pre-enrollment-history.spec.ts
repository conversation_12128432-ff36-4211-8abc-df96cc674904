import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test(`User get pre enrollment history with valid permission should return Success`, async ({
    preEnrollmentService,
    configuration,
    authenticationService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getPreEnrollmentReservation = await preEnrollmentService.getPreEnrollmentReservation();
    await expect(getPreEnrollmentReservation).toBeOK();
    const getPreEnrollmentReservationResp = await getPreEnrollmentReservation.json();
    expect(getPreEnrollmentReservationResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'success',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getPreEnrollmentReservationResp.data.total).toBeGreaterThan(0);
  });
});

test.describe('Permission', () => {
  test(`User get pre enrollment history without valid permission should return Forbidden`, async ({
    configuration,
    authenticationService,
    preEnrollmentService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getPreEnrollmentReservation = await preEnrollmentService.getPreEnrollmentReservation();
    await expect(getPreEnrollmentReservation).not.toBeOK();
    const getPreEnrollmentReservationResp = await getPreEnrollmentReservation.json();
    expect(getPreEnrollmentReservationResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
