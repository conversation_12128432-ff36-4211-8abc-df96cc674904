import { expect } from '@playwright/test';
import { CalendarHelper } from '../../../shared/calendar/calendar-helper';
import { ExcelTestData } from '../../../shared/excel/excel-test-data';
import { test as base } from '../../fixtures/default-fixture';
import { EnrollType } from '../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let fileBuffer;

export const test = base.extend<{
  bulkRoundExcel: ExcelTestData;
  bulkEnrollExcel: ExcelTestData;
}>({
  bulkEnrollExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkEnrollShortTemplate();
    use(ExcelTestData);
  },
  bulkRoundExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkRoundTemplate();
    use(ExcelTestData);
  },
});

test.beforeEach(async ({ roundRepo, enrollmentsRepo, configuration }) => {
  const courseRegular = configuration.shareCourses.regularCourseWithRound;
  const courseOIC = configuration.shareCourses.oicAutoApproveCourse;
  const userTLI = configuration.usersLocal.userTLIAPI;
  const courses = [courseRegular, courseOIC];

  //Clean round and enrollment
  for (const courseEnrollment of courses) {
    await roundRepo.deleteTrainingRoundByCourseId(courseEnrollment.courseId);
    await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);
  }
});

test.afterEach(async ({ configuration, roundRepo, enrollmentsRepo }) => {
  const courseRegular = configuration.shareCourses.regularCourseWithRound;
  const courseOIC = configuration.shareCourses.oicAutoApproveCourse;
  const userTLI = configuration.usersLocal.userTLIAPI;
  const courses = [courseRegular, courseOIC];

  //Clean round and enrollment
  for (const courseEnrollment of courses) {
    await roundRepo.deleteTrainingRoundByCourseId(courseEnrollment.courseId);
    await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);
  }
});

test.describe('Positive', () => {
  test(`User create regular pre enrollment with valid permission should return Success`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
    roundsService,
    bulkRoundExcel,
  }) => {
    const courseRegular = configuration.shareCourses.regularCourseWithRound;
    const userTLI = configuration.usersLocal.userTLIAPI;

    const adminTLI = configuration.usersLocal.adminTLIAPI;

    const currentDate = new Date();
    const futureDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const roundDate = new Date(new Date().setDate(futureDate.getDate() + 2));
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));

    bulkRoundExcel.addRow({
      courseCode: courseRegular.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    fileBuffer = await bulkRoundExcel.writeExcelStream();

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseRegular.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(fileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );
  });

  test(`User create OIC pre enrollment with valid permission should return Success`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
    bulkRoundExcel,
    roundsService,
  }) => {
    const courseRegular = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    const currentDate = new Date();
    const futureDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const roundDate = new Date(new Date().setDate(futureDate.getDate() + 2));
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));

    bulkRoundExcel.addRow({
      courseCode: courseRegular.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    fileBuffer = await bulkRoundExcel.writeExcelStream();

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseRegular.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(fileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );
  });

  test(`User create regular pre enrollment report with valid permission should return Success`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
    roundsService,
    bulkRoundExcel,
    reportService,
  }) => {
    const currentDate = new Date();
    const courseRegular = configuration.shareCourses.regularCourseWithRound;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    const startDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() - 1)),
    );
    const endDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() + 1)),
    );
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    const futureDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const roundDate = new Date(new Date().setDate(futureDate.getDate() + 2));
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseRegular.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    bulkRoundExcel.addRow({
      courseCode: courseRegular.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    fileBuffer = await bulkRoundExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(fileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );

    const exportRegularPreEnrollmentTransactionReport = await reportService.exportRegularPreEnrollmentTransactionReport(
      startDate,
      endDate,
      'PASSED',
    );
    await expect(exportRegularPreEnrollmentTransactionReport).toBeOK();
    const exportRegularPreEnrollmentTransactionReportResp = await exportRegularPreEnrollmentTransactionReport.json();
    expect(exportRegularPreEnrollmentTransactionReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate regular pre-enrollments report successfully',
      }),
    );
  });

  test(`User create OIC pre enrollment report with valid permission should return Success`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
    roundsService,
    bulkRoundExcel,
    reportService,
  }) => {
    const currentDate = new Date();
    const courseOIC = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    const startDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() - 1)),
    );
    const endDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() + 1)),
    );
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    const futureDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const roundDate = new Date(new Date().setDate(futureDate.getDate() + 2));
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseOIC.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    bulkRoundExcel.addRow({
      courseCode: courseOIC.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    fileBuffer = await bulkRoundExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(fileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );

    const exportRegularPreEnrollmentTransactionReport = await reportService.exportOICPreEnrollmentTransactionReport(
      startDate,
      endDate,
      'PASSED',
    );
    await expect(exportRegularPreEnrollmentTransactionReport).toBeOK();
    const exportRegularPreEnrollmentTransactionReportResp = await exportRegularPreEnrollmentTransactionReport.json();
    expect(exportRegularPreEnrollmentTransactionReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate pre-enrollments report successfully',
      }),
    );
  });
});

test.describe('Permission', () => {
  test(`User create regular pre enrollment without valid permission should return Forbidden`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
  }) => {
    const roundDate = new Date();
    const courseRegular = configuration.shareCourses.regularCourseWithRound;
    const userTLI = configuration.usersLocal.userTLIAPI;

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseRegular.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      userTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).not.toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User create OIC pre enrollment without valid permission should return Forbidden`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
  }) => {
    const roundDate = new Date();
    const courseRegular = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.usersLocal.userTLIAPI;

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseRegular.courseCode,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      userTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).not.toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User create regular pre enrollment report without valid permission should return Forbidden`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
    reportService,
  }) => {
    const currentDate = new Date();
    const courseRegular = configuration.shareCourses.regularCourseWithRound;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const startDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() - 1)),
    );
    const endDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() + 1)),
    );
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseRegular.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      currentDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportRegularPreEnrollmentTransactionReport = await reportService.exportRegularPreEnrollmentTransactionReport(
      startDate,
      endDate,
      'PASSED',
    );
    await expect(exportRegularPreEnrollmentTransactionReport).not.toBeOK();
    const exportRegularPreEnrollmentTransactionReportResp = await exportRegularPreEnrollmentTransactionReport.json();
    expect(exportRegularPreEnrollmentTransactionReportResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User create OIC pre enrollment report with valid permission should return Success`, async ({
    preEnrollmentService,
    bulkEnrollExcel,
    configuration,
    authenticationService,
    roundsService,
    bulkRoundExcel,
    reportService,
  }) => {
    const currentDate = new Date();
    const courseOIC = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    const startDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() - 1)),
    );
    const endDateStr = CalendarHelper.getShortBuddhistDateString(
      new Date(new Date().setDate(currentDate.getDate() + 1)),
    );
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    const futureDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const roundDate = new Date(new Date().setDate(futureDate.getDate() + 2));
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: courseOIC.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    bulkRoundExcel.addRow({
      courseCode: courseOIC.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    fileBuffer = await bulkRoundExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(fileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportRegularPreEnrollmentTransactionReport = await reportService.exportOICPreEnrollmentTransactionReport(
      startDate,
      endDate,
      'PASSED',
    );
    await expect(exportRegularPreEnrollmentTransactionReport).not.toBeOK();
    const exportRegularPreEnrollmentTransactionReportResp = await exportRegularPreEnrollmentTransactionReport.json();
    expect(exportRegularPreEnrollmentTransactionReportResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
