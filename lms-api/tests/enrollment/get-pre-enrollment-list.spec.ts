import { expect } from '@playwright/test';
import { CalendarHelper } from '../../../shared/calendar/calendar-helper';
import { ExcelTestData } from '../../../shared/excel/excel-test-data';
import { test as base } from '../../fixtures/default-fixture';
import { EnrollType } from '../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let resetTimestamp: number;
let fileBuffer;

export const test = base.extend<{
  bulkEnrollExcel: ExcelTestData;
  bulkRoundExcel: ExcelTestData;
}>({
  bulkEnrollExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkEnrollShortTemplate();
    use(ExcelTestData);
  },
  bulkRoundExcel: async ({ excelTemplateService }, use) => {
    const ExcelTestData = await excelTemplateService.getExcelBulkRoundTemplate();
    use(ExcelTestData);
  },
});

test.beforeEach(
  async ({
    roundRepo,
    enrollmentsRepo,
    configuration,
    bulkEnrollExcel,
    authenticationService,
    preEnrollmentService,
    bulkRoundExcel,
    roundsService,
  }) => {
    const course = configuration.shareCourses.regularCourseWithRound;
    const userTLI = configuration.usersLocal.userTLIAPI;
    const adminTLI = configuration.usersLocal.adminTLIAPI;

    //Clean round and enrollment
    await roundRepo.deleteTrainingRoundByCourseId(course.courseId);
    await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);

    const currentDate = new Date();
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);
    resetTimestamp = new Date().getTime();

    bulkRoundExcel.addRow({
      courseCode: course.courseCode,
      roundDate: `${CalendarHelper.getShortBuddhistDateString(roundDate)}`,
      firstRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(firstRegisDate)} ${firstRegisDate
        .toTimeString()
        .slice(0, 5)}`,
      lastRegistrationDate: `${CalendarHelper.getShortBuddhistDateString(lastRegisDate)} ${lastRegisDate
        .toTimeString()
        .slice(0, 5)}`,
    });
    fileBuffer = await bulkRoundExcel.writeExcelStream();

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      username: userTLI.username,
      courseCode: course.courseCode,
      enroll_type: EnrollType.voluntary,
    });
    const bulkEnrollBuffer = await bulkEnrollExcel.writeExcelStream();

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);

    const createRound = await roundsService.createRound(fileBuffer);
    await expect(createRound).toBeOK();
    const createRoundResp = await createRound.json();
    expect(createRoundResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        domain: 'round',
        message: 'create round success',
      }),
    );

    const createBulkPreEnrollmentRegular = await preEnrollmentService.createBulkPreEnrollment(
      adminTLI.organizationId,
      bulkEnrollBuffer,
      roundDate,
    );
    await expect(createBulkPreEnrollmentRegular).toBeOK();
    const createBulkPreEnrollmentRegularResp = await createBulkPreEnrollmentRegular.json();
    expect(createBulkPreEnrollmentRegularResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );
  },
);

test.afterEach(async ({ configuration, roundRepo, enrollmentsRepo }) => {
  const courses = configuration.shareCourses.regularCourseWithRound;
  const userTLI = configuration.usersLocal.userTLIAPI;

  //Clean round and enrollment
  await roundRepo.deleteTrainingRoundByCourseId(courses.courseId);
  await enrollmentsRepo.deleteAllEnrollmentsForUser(userTLI.userId);
});

test.describe('Positive', () => {
  test(`User get list of pre enrollment with valid permission should return Success`, async ({ 
    preEnrollmentService,
  }) => {
    const roundDate = new Date();
    const startDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const endDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));

    const getPreEnrollmentList = await preEnrollmentService.getPreEnrollmentList(startDate, endDate);
    await expect(getPreEnrollmentList).toBeOK();
    const getPreEnrollmentListResp = await getPreEnrollmentList.json();
    expect(getPreEnrollmentListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Get regular pre-enrollments successfully',
        data: expect.objectContaining({
          total: expect.any(Number),
        }),
      }),
    );
    expect(getPreEnrollmentListResp.data.total).toBeGreaterThan(0);
  });
});

test.describe('Permission', () => {
  test(`User get list of pre enrollment without valid permission should return Forbidden`, async ({
    configuration,
    authenticationService,
    preEnrollmentService,
  }) => {
    const roundDate = new Date();
    const startDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const endDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    const userTLI = configuration.usersLocal.userTLIAPI;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getPreEnrollmentList = await preEnrollmentService.getPreEnrollmentList(startDate, endDate);
    await expect(getPreEnrollmentList).not.toBeOK();
    const getPreEnrollmentListResp = await getPreEnrollmentList.json();
    expect(getPreEnrollmentListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
