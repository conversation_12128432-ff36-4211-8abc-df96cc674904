import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test(`User with valid permission get Knowledge Content report list should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const menuKey = 'KnowledgeContentReport';

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get organization report list',
      }),
    );
  });

  test(`User with valid permission export Knowledge Content Report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;
    const currentDate = new Date();
    const startAt = new Date();
    startAt.setDate(currentDate.getDate() - 1);

    const endAt = new Date();
    endAt.setDate(currentDate.getDate() + 1);

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportKnowledgeContentReport(
      organizationReports.AllEducationalMediaReport.id,
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export ASC report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(organizationReports.ASC.id, 1, '2025');
    await expect(exportReport).toBeOK();
  });
});

test.describe('Permission', () => {
  test.skip(`User without valid permission get tsi report list should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const menuKey = 'KnowledgeContentReport';

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).not.toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test.skip(`User with valid permission export Knowledge Content Report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;
    const currentDate = new Date();
    const startAt = new Date();
    startAt.setDate(currentDate.getDate() - 1);

    const endAt = new Date();
    endAt.setDate(currentDate.getDate() + 1);

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportKnowledgeContentReport(
      organizationReports.AllEducationalMediaReport.id,
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
    );
    await expect(exportReport).not.toBeOK();
  });

  test.skip(`User with valid permission export ASC report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(organizationReports.ASC.id, 1, '2025');
    await expect(exportReport).not.toBeOK();
  });
});
