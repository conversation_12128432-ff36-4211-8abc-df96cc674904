import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';

test.describe('Positive', () => {
  test(`User with valid permission get tsi report list should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const menuKey = 'TSIReport';

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get organization report list',
      }),
    );
  });
});

test.describe('Permission', () => {
  test.skip(`User without valid permission get tsi report list should return FORBIDDEN`, async ({// No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const menuKey = 'TSIReport';

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).not.toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
