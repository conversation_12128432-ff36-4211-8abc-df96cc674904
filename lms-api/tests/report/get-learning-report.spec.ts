import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';
import { ReportStatus } from '../../../shared/repositories/lms/constants/enums/report.enum';

test.describe('Positive', () => {
  test(`User with valid permission get enrollment report list should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const menuKey = 'EnrollmentReport';

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get organization report list',
      }),
    );
  });

  test(`User with valid permission export Learner report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 1));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 1));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportLearnerReport = await reportService.exportLearnerReport(
      [],
      [],
      [startAt, endAt],
      [],
      [],
      [],
      [],
      [startAt, endAt],
    );
    await expect(exportLearnerReport).toBeOK();
    const exportLearnerReportResp = await exportLearnerReport.json();
    expect(exportLearnerReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.learnerReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportLearnerReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export Monthly Summary Enrollment Renewal - 3 report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.MonthlySummaryEnrollmentRenewal3.id,
      1,
      '2025',
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export Monthly Summary Training Participants report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.MonthlySummaryTrainingParticipants.id,
      1,
      '2025',
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export Annual Summary Trainees Life Insurance Agent License Application And Renewal Courses report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.AnnualSummaryTraineesLifeInsuranceAgentLicenseApplicationAndRenewalCourses.id,
      1,
      '2025',
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export Annual Summary Training Participants report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.AnnualSummaryTrainingParticipants.id,
      1,
      '2025',
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export Public Relations Course Report report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;
    const currentDate = new Date();
    const startAt = new Date();
    startAt.setDate(currentDate.getDate() - 1);

    const endAt = new Date();
    endAt.setDate(currentDate.getDate() + 1);

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.PublicRelationsCourseReport.id,
      '',
      '',
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export Universal Report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;
    const currentDate = new Date();
    const startAt = new Date();
    startAt.setDate(currentDate.getDate() - 1);

    const endAt = new Date();
    endAt.setDate(currentDate.getDate() + 1);

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.UniversalReport.id,
      '',
      '',
      '',
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
    );
    await expect(exportReport).toBeOK();
  });

  test(`User with valid permission export Survey Submission Report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const materialMediaId = '5f2cb982-be1e-4214-a3be-0a28e8bb32ca';
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportSurveySubmissionReport = await reportService.exportSurveySubmissionReport(
      null,
      null,
      materialMediaId,
      startAt,
      endAt,
    );
    await expect(exportSurveySubmissionReport).toBeOK();
    const exportSurveySubmissionReportResp = await exportSurveySubmissionReport.json();
    expect(exportSurveySubmissionReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success',
      }),
    );
  });
});

test.describe('Permission', () => {
  test.skip(`User without valid permission get enrollment report list should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const menuKey = 'EnrollmentReport';

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).not.toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission export Learner report should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 1));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 1));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportLearnerReport = await reportService.exportLearnerReport(
      [],
      [],
      [startAt, endAt],
      [],
      [],
      [],
      [],
      [startAt, endAt],
    );
    await expect(exportLearnerReport).not.toBeOK();
    const exportLearnerReportResp = await exportLearnerReport.json();
    expect(exportLearnerReportResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test.skip(`User without valid permission export Monthly Summary Enrollment Renewal - 3 report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.MonthlySummaryEnrollmentRenewal3.id,
      1,
      '2025',
    );
    await expect(exportReport).not.toBeOK();
  });

  test.skip(`User without valid permission export Monthly Summary Training Participants report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.MonthlySummaryTrainingParticipants.id,
      1,
      '2025',
    );
    await expect(exportReport).not.toBeOK();
  });

  test.skip(`User without valid permission export Annual Summary Trainees Life Insurance Agent License Application And Renewal Courses report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.AnnualSummaryTraineesLifeInsuranceAgentLicenseApplicationAndRenewalCourses.id,
      1,
      '2025',
    );
    await expect(exportReport).not.toBeOK();
  });

  test.skip(`User without valid permission export Annual Summary Training Participants report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.AnnualSummaryTrainingParticipants.id,
      1,
      '2025',
    );
    await expect(exportReport).not.toBeOK();
  });

  test.skip(`User without valid permission export Public Relations Course Report report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;
    const currentDate = new Date();
    const startAt = new Date();
    startAt.setDate(currentDate.getDate() - 1);

    const endAt = new Date();
    endAt.setDate(currentDate.getDate() + 1);

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.PublicRelationsCourseReport.id,
      '',
      '',
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
    );
    await expect(exportReport).not.toBeOK();
  });

  test.skip(`User without valid permission export Universal Report should return FORBIDDEN`, async ({
    // No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const organizationReports = configuration.shareReport.organizationReports;
    const currentDate = new Date();
    const startAt = new Date();
    startAt.setDate(currentDate.getDate() - 1);

    const endAt = new Date();
    endAt.setDate(currentDate.getDate() + 1);

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportReport = await reportService.exportReport(
      organizationReports.UniversalReport.id,
      '',
      '',
      '',
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
      `${startAt.toISOString().split('T')[0]},${endAt.toISOString().split('T')[0]}`,
    );
    await expect(exportReport).not.toBeOK();
  });

  test(`User without valid permission export Survey Submission Report should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const materialMediaId = '5f2cb982-be1e-4214-a3be-0a28e8bb32ca';
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportSurveySubmissionReport = await reportService.exportSurveySubmissionReport(
      null,
      null,
      materialMediaId,
      startAt,
      endAt,
    );
    await expect(exportSurveySubmissionReport).not.toBeOK();
    const exportSurveySubmissionReportResp = await exportSurveySubmissionReport.json();
    expect(exportSurveySubmissionReportResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
