import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';
import { ReportStatus } from '../../../shared/repositories/lms/constants/enums/report.enum';

test.describe('Positive', () => {
  test(`User with valid permission get oic report list should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const menuKey = 'OICReport';

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get organization report list',
      }),
    );
  });

  test(`User with valid permission export OIC pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.OIC0,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC2 pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.OIC2,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC3 pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.OIC3,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC4 pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.OIC4,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UL pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.UL,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UK pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseTypeAllAndUK,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.UK,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC regulator pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPreReport,
      reportOIC.licenseRenewal.OIC0,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC2 regulator pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPreReport,
      reportOIC.licenseRenewal.OIC2,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC3 regulator pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPreReport,
      reportOIC.licenseRenewal.OIC3,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC4 regulator pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPreReport,
      reportOIC.licenseRenewal.OIC4,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UL regulator pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPreReport,
      reportOIC.licenseRenewal.UL,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UK regulator pre report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseTypeAllAndUK,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPreReport,
      reportOIC.licenseRenewal.UK,
    );
    await expect(exportOICRegulatorPreReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPreReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPostReport,
      reportOIC.licenseRenewal.OIC0,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 8_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC2 post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPostReport,
      reportOIC.licenseRenewal.OIC2,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC3 post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPostReport,
      reportOIC.licenseRenewal.OIC3,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC4 post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPostReport,
      reportOIC.licenseRenewal.OIC4,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UL post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPostReport,
      reportOIC.licenseRenewal.UL,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UK post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseTypeAllAndUK,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPostReport,
      reportOIC.licenseRenewal.UK,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC regulator post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPostReport,
      reportOIC.licenseRenewal.OIC0,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 8_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC2 regulator post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPostReport,
      reportOIC.licenseRenewal.OIC2,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC3 regulator post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPostReport,
      reportOIC.licenseRenewal.OIC3,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export OIC4 regulator post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPostReport,
      reportOIC.licenseRenewal.OIC4,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UL regulator post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPostReport,
      reportOIC.licenseRenewal.UL,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });

  test(`User with valid permission export UK regulator post report should return SUCCESS`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const adminTLI = configuration.usersLocal.adminTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 5));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 5));

    await authenticationService.login(adminTLI.username, adminTLI.password, adminTLI.organizationId);
    const exportOICRegulatorPostReport = await reportService.exportOICRegulatorPostReport(
      reportOIC.applicantType,
      reportOIC.licenseTypeAllAndUK,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicRegulatorPostReport,
      reportOIC.licenseRenewal.UK,
    );
    await expect(exportOICRegulatorPostReport).toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPostReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Generate report successfully',
        data: expect.objectContaining({
          type: ReportStatus.oicRegulatorPostReport,
          status: ReportStatus.pending,
        }),
      }),
    );
    const reportId = exportOICRegulatorPreReportResp.data.id;
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    const getReportHistoriesList = await reportService.getReportHistoriesList();
    await expect(getReportHistoriesList).toBeOK();
    const getReportHistoriesListResp = await getReportHistoriesList.json();
    expect(getReportHistoriesListResp).toEqual(
      expect.objectContaining({
        status: 'SUCCESS',
        message: 'Success get list',
        data: expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: reportId,
              status: ReportStatus.completed,
            }),
          ]),
        }),
      }),
    );
  });
});

test.describe('Permission', () => {
  test.skip(`User without valid permission get oic report list should return FORBIDDEN`, async ({// No block permission
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const menuKey = 'OICReport';

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const getReportList = await reportService.getReportList(menuKey);
    await expect(getReportList).not.toBeOK();
    const getReportListResp = await getReportList.json();
    expect(getReportListResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });

  test(`User without valid permission export OIC pre report should return FORBIDDEN`, async ({
    configuration,
    authenticationService,
    reportService,
  }) => {
    const userTLI = configuration.usersLocal.userTLIAPI;
    const reportOIC = configuration.shareReport.ReportOIC;
    const currentDate = new Date();
    const startAt = new Date(new Date().setMonth(currentDate.getDate() - 30));
    const endAt = new Date(new Date().setMonth(currentDate.getDate() + 30));

    await authenticationService.login(userTLI.username, userTLI.password, userTLI.organizationId);
    const exportOICRegulatorPreReport = await reportService.exportOICRegulatorPreReport(
      reportOIC.applicantType,
      reportOIC.licenseType,
      reportOIC.preEnrollmentStatus,
      startAt,
      endAt,
      reportOIC.trainingCenter,
      reportOIC.reportType.oicPreReport,
      reportOIC.licenseRenewal.OIC0,
    );
    await expect(exportOICRegulatorPreReport).not.toBeOK();
    const exportOICRegulatorPreReportResp = await exportOICRegulatorPreReport.json();
    expect(exportOICRegulatorPreReportResp).toEqual(
      expect.objectContaining({
        status: 'FORBIDDEN',
        message: 'Forbidden.',
      }),
    );
  });
});
