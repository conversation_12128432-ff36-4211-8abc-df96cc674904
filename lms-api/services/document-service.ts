import { APIResponse } from '@playwright/test';
import dayjs from 'dayjs';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class DocumentService extends BaseAPIService {
  endpointEnrollmentAttachments: string;
  endpointEnrollment: string;
  endpointMeV1: string;
  endpointEnrollments: string;
  endpointAttachments: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/enrollments-v2';
    this.endpointEnrollmentAttachments = '/enrollment-attachments';
    this.endpointEnrollment = '/enrollment';
    this.endpointMeV1 = '/me-v1/me';
    this.endpointEnrollments = '/enrollments';
    this.endpointAttachments = '/attachments';
  }

  async getEnrollmentAdditionalAttachmentsList(
    firstname?: string,
    email?: string,
    courseName?: string,
    status?: string,
    startDate?: Date,
    endDate?: Date,
    withToken = true,
  ): Promise<APIResponse> {
    const params = new URLSearchParams({
      'enrollment_attachment.user_fullname[0][field]': 'firstname',
      'enrollment_attachment.user_fullname[0][value]': firstname,
      'enrollment_attachment.user_email[0][field]': 'email',
      'enrollment_attachment.user_email[0][value]': email,
      'enrollment_attachment.course_name[0][field]': 'name',
      'enrollment_attachment.course_name[0][value]': courseName,
      'enrollment_attachment.additionalDocStatus[0][field]': 'status',
      'enrollment_attachment.additionalDocStatus[0][value]': status,
      'enrollment_attachment.createdAt[0][field]': 'createdAt',
      'enrollment_attachment.createdAt[0][value][0]': startDate ? dayjs(startDate).format('YYYY-MM-DD') : '',
      'enrollment_attachment.createdAt[0][value][1]': endDate ? dayjs(endDate).format('YYYY-MM-DD') : '',
      page: '1',
      pageSize: '20',
      fileType: 'additional',
    });

    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}?${params.toString()}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }

  async getEnrollmentDeductAttachmentsList(
    firstname?: string,
    email?: string,
    citizenId?: string,
    courseName?: string,
    status?: string,
    startDate?: Date,
    endDate?: Date,
    withToken = true,
  ): Promise<APIResponse> {
    const params = new URLSearchParams({
      'enrollment_attachment.user_fullname[0][field]': 'firstname',
      'enrollment_attachment.user_fullname[0][value]': firstname,
      'enrollment_attachment.user_email[0][field]': 'email',
      'enrollment_attachment.user_email[0][value]': email,
      'enrollment_attachment.user_citizenId[0][field]': 'citizenId',
      'enrollment_attachment.user_citizenId[0][value]': citizenId,
      'enrollment_attachment.course_name[0][field]': 'name',
      'enrollment_attachment.course_name[0][value]': courseName,
      'enrollment_attachment.deductDocStatus[0][field]': 'status',
      'enrollment_attachment.deductDocStatus[0][value]': status,
      'enrollment_attachment.startedAt[0][field]': 'startedAt',
      'enrollment_attachment.startedAt[0][value][0]': startDate ? dayjs(startDate).format('YYYY-MM-DD') : '',
      'enrollment_attachment.startedAt[0][value][1]': endDate ? dayjs(endDate).format('YYYY-MM-DD') : '',
      page: '1',
      pageSize: '20',
      fileType: 'deduct',
    });

    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}?${params.toString()}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }

  async getAttachmentAdditionalDetail(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }

  async getAttachmentDeductDetail(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }

  async cancelRequestAttachment(id: string, reason: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}/${id}/status`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          reason: reason,
          status: 'CANCELED',
        },
      });
    return resp;
  }

  async sendDeductDocument(
    id: string,
    attachmentId: string,
    payload: {
      information: {
        salute: string;
        firstname: string;
        lastname: string;
        citizenId: string;
      };
      criteria: {
        certificateType: Record<
          string,
          {
            checked: boolean;
            issuedBy: string;
            effectedDate: string;
            expiredDate?: string;
          }
        >;
        educateMasterDegree: {
          checked: boolean;
          universityName: string;
          universityCode: string;
          effectedDate: string;
          studyMajor: string;
        };
        instructor: {
          checked: boolean;
          startedDate: string;
          endedDate: string;
          courseRef: string;
          program: string;
          hours: number;
        };
      };
      fileInfo: {
        description: string;
        files: {
          fileName: string;
          filePath: string;
          originalFileName: string;
        }[];
      };
    },
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(
        `${this.baseApiUrl}${this.endpointMeV1}${this.endpointEnrollments}/${id}${this.endpointAttachments}/${attachmentId}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          data: {
            payload: payload,
          },
        },
      );
    return resp;
  }

  async getRequestAttachmentDetail(
    id: string,
    attachmentId: string,
    fileType: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(
        `${this.baseApiUrl}${this.endpointMeV1}${this.endpointEnrollments}/${id}${this.endpointAttachments}/${attachmentId}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          params: {
            fileType: fileType,
          },
        },
      );
    return resp;
  }

  async cancelDeductAttachment(
    id: string,
    criteria: {
      information: {
        salute: string;
        firstname: string;
        lastname: string;
        citizenId: string;
      };
      criteria: {
        certificateType: Record<
          string,
          {
            checked: boolean;
            issuedBy: string;
            isVerified: boolean;
            effectedDate: string;
            expiredDate?: string;
          }
        >;
        educateMasterDegree: {
          checked: boolean;
          universityName: string;
          universityCode: string;
          effectedDate: string;
          isVerified: boolean;
          studyMajor: string;
        };
        instructor: {
          checked: boolean;
          startedDate: string;
          endedDate: string;
          courseRef: string;
          program: string;
          hours: number;
          isVerified: boolean;
          issuedBy: string;
        };
      };
      fileInfo: {
        description: string;
        files: {
          fileName: string;
          filePath: string;
          originalFileName: string;
        }[];
      };
    },
    reason: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}/${id}/status`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          criteria,
          reason: reason,
          status: 'REJECTED',
        },
      });
    return resp;
  }

  async approveDeductAttachment(
    id: string,
    criteria: {
      information: {
        salute: string;
        firstname: string;
        lastname: string;
        citizenId: string;
      };
      criteria: {
        certificateType: Record<
          string,
          {
            checked: boolean;
            issuedBy: string;
            isVerified: boolean;
            effectedDate: string;
            expiredDate?: string;
          }
        >;
        educateMasterDegree: {
          checked: boolean;
          universityName: string;
          universityCode: string;
          effectedDate: string;
          isVerified: boolean;
          studyMajor: string;
        };
        instructor: {
          checked: boolean;
          startedDate: string;
          endedDate: string;
          courseRef: string;
          program: string;
          hours: number;
          isVerified: boolean;
          issuedBy: string;
        };
      };
      fileInfo: {
        description: string;
        files: {
          fileName: string;
          filePath: string;
          originalFileName: string;
        }[];
      };
    },
    reason: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointEnrollmentAttachments}/${id}/status`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          criteria,
          reason: reason,
          status: 'APPROVED',
        },
      });
    return resp;
  }
}
