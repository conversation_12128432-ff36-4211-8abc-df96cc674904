import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class KnowledgeContentService extends BaseAPIService {
  endpointKnowledgeContentItem: string;
  endpointKnowledgeContent: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/knowledge-content-items-v1';
    this.endpointKnowledgeContentItem = '/knowledge-content-items';
    this.endpointKnowledgeContent = '/knowledge-contents';
  }

  async getKnowledgeContentList(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async createKnowledgeContent(title: string, code: string, type: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          title: title,
          code: code,
          type: type,
        },
      });
    return resp;
  }

  async updateKnowledgeContent(
    id: string,
    code: string,
    title: string,
    type: string,
    description?: string,
    thumbnailMediaId?: string,
    instructorIds?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          code: code,
          description: description,
          instructorIds: instructorIds,
          thumbnailMediaId: thumbnailMediaId,
          title: title,
          type: type,
        },
      });
    return resp;
  }

  async updateArticleKnowledgeContent(id: string, content: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/update-media`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          article: {
            contentHtml: content,
          },
        },
      });

    return resp;
  }

  async updateEbookKnowledgeContent(id: string, content: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/update-media`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          ebook: {
            mediaId: content,
          },
        },
      });

    return resp;
  }

  async updateVideoKnowledgeContent(
    id: string,
    media: Record<string, any>,
    mediaTranscode: Record<string, any>,
    duration: number,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/update-media`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isDownloadEnabled: false,
          video: {
            duration: duration,
            media: media,
            mediaTranscode: mediaTranscode,
          },
        },
      });

    return resp;
  }

  async updatePodcastKnowledgeContent(
    id: string,
    media: Record<string, any>,
    mediaTranscode: Record<string, any>,
    duration: number,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/update-media`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isDownloadEnabled: false,
          podcast: {
            duration: duration,
            media: media,
            mediaTranscode: mediaTranscode,
          },
        },
      });

    return resp;
  }

  async updateKnowledgeContentAttachment(id: string, mediaIds: string[], withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/attachments`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          mediaIds: [mediaIds],
        },
      });
    return resp;
  }

  async updateKnowledgeContentCategory(id: string, categoryIds: string[], withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/categories`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          categoryIds: categoryIds,
        },
      });
    return resp;
  }

  async updateKnowledgeContentComment(id: string, isCommentEnabled: boolean, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContent}/${id}/isCommentEnabled`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          isCommentEnabled: isCommentEnabled,
        },
      });
    return resp;
  }

  async updateKnowledgeContentStatus(
    id: string,
    action: string,
    publishedEndAt?: Date | '',
    publishedStartAt?: Date | '',
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}/status`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          action: action,
          publishedStartAt: publishedStartAt ?? '',
          publishedEndAt: publishedEndAt ?? '',
        },
      });
    return resp;
  }

  async deleteKnowledgeContent(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .delete(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async getKnowledgeContentDetail(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointKnowledgeContentItem}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }
}
