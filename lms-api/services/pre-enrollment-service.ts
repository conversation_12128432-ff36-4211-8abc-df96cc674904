import { APIResponse } from '@playwright/test';
import dayjs from 'dayjs';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class PreEnrollmentService extends BaseAPIService {
  endpointRegularPreEnrollmentTransactions: string;
  endpointPreEnrollmentReservationsV2: string;
  endpointOrganizations: string;
  endpointPreEnrollmentReservations: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/pre-enrollment-transactions-v2';
    this.endpointRegularPreEnrollmentTransactions = '/regular-pre-enrollment-transactions';
    this.endpointPreEnrollmentReservationsV2 = '/pre-enrollment-reservations-v2';
    this.endpointOrganizations = '/organizations';
    this.endpointPreEnrollmentReservations = '/pre-enrollment-reservations';
  }

  async getPreEnrollmentList(startDate?: Date, endDate?: Date, withToken = true): Promise<APIResponse> {
    const params = new URLSearchParams({
      'pre_enrollment.round_date[0][field]': 'roundDate',
      'pre_enrollment.round_date[0][value][0]': startDate ? dayjs(startDate).format('YYYY-MM-DD') : '',
      'pre_enrollment.round_date[0][value][1]': endDate ? dayjs(endDate).format('YYYY-MM-DD') : '',
      page: '1',
      pageSize: '20',
    });

    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointRegularPreEnrollmentTransactions}?${params.toString()}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }

  async createBulkPreEnrollment(
    organizationId: string,
    excelPath: Buffer,
    roundDate: Date,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(
        `${this.baseApiUrl}${this.endpointPreEnrollmentReservationsV2}${this.endpointOrganizations}/${organizationId}/bulk-user-validate`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          multipart: {
            excelFile: {
              name: 'bulk-enrollment-lms-template.xlsx',
              mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              buffer: excelPath,
            },
            roundDate: roundDate.toISOString(),
          },
        },
      );
    await new Promise((resolve) => setTimeout(resolve, 3000));

    return resp;
  }

  async getPreEnrollmentReservation(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpointPreEnrollmentReservationsV2}${this.endpointPreEnrollmentReservations}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });

    return resp;
  }
}
