import fs from 'fs';
import path from 'path';
import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class MediaService extends BaseAPIService {
  endpointMedia: string;
  endpointAPIUpload: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/media-v1';
    this.endpointMedia = '/media';
    this.endpointAPIUpload = '/api/upload';
  }

  async upload(filePath: string, withToken = true): Promise<APIResponse> {
    const fileBuffer = fs.readFileSync(filePath);

    const resp = await this.contextManager.getContext().post(`${this.baseUrl}${this.endpointAPIUpload}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      multipart: {
        file: {
          name: filePath.split('/').pop()!,
          mimeType: 'image/jpeg',
          buffer: fileBuffer,
        },
      },
    });
    return resp;
  }

  async createMedia(filePath: string, withToken = true): Promise<APIResponse> {
    const fileBuffer = fs.readFileSync(filePath);
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointMedia}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        multipart: {
          uploadedFile: {
            name: path.basename(filePath),
            mimeType: 'image/jpeg',
            buffer: fileBuffer,
          },
        },
      });
    return resp;
  }
}
