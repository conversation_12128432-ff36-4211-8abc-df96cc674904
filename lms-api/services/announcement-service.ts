import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class AnnouncementService extends BaseAPIService {
  endpointAnnouncements: string;
  endpointHighlightAnnouncements: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/announcements-v1';
    this.endpointAnnouncements = '/announcements';
    this.endpointHighlightAnnouncements = '/highlight-announcements';
  }

  async getAnnouncementList(
    title?: string,
    status?: string,
    authors?: string,
    publishedStartAt?: Date,
    publishedStartAtTo?: Date,
    publishedEndAt?: Date,
    publishedEndAtTo?: Date,
    updatedAt?: Date,
    updatedAtTo?: Date,
    withToken = true,
  ): Promise<APIResponse> {
    const params = new URLSearchParams();

    if (title) params.set('title', title);
    if (status) params.set('status', status);
    if (authors) params.append('authors[]', authors);

    if (publishedStartAt) params.append('publishedStartAt[]', publishedStartAt.toISOString());
    if (publishedStartAtTo) params.append('publishedStartAt[]', publishedStartAtTo.toISOString());

    if (publishedEndAt) params.append('publishedEndAt[]', publishedEndAt.toISOString());
    if (publishedEndAtTo) params.append('publishedEndAt[]', publishedEndAtTo.toISOString());

    if (updatedAt) params.append('updatedAt[]', updatedAt.toISOString());
    if (updatedAtTo) params.append('updatedAt[]', updatedAtTo.toISOString());

    params.set('page', '1');
    params.set('pageSize', '20');

    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}?${params.toString()}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async getAnnouncementDetail(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async createAnnouncementHighlight(
    highlights: { id: string; highlightIndex: number }[],
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointHighlightAnnouncements}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: highlights,
      });

    return resp;
  }

  async createAnnouncement(title: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          title: title,
        },
      });
    return resp;
  }

  async updateAnnouncement(
    id: string,
    authors: string,
    title: string,
    contentHtml?: string,
    thumbnailMediaId?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          authors: authors,
          contentHtml: contentHtml,
          thumbnailMediaId: thumbnailMediaId,
          title: title,
        },
      });
    return resp;
  }

  async updateAnnouncementAttachment(id: string, attachmentIds: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}/${id}/attachments`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          attachmentIds: [attachmentIds],
        },
      });
    return resp;
  }

  async updateAnnouncementStatus(
    id: string,
    action: string,
    publishedEndAt?: Date | '',
    publishedStartAt?: Date | '',
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}/${id}/status`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          action: action,
          publishedStartAt: publishedStartAt ?? '',
          publishedEndAt: publishedEndAt ?? '',
        },
      });
    return resp;
  }

  async deleteAnnouncement(id: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .delete(`${this.baseApiUrl}${this.endpoint}${this.endpointAnnouncements}/${id}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }
}
