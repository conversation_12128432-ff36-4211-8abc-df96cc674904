import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { test } from '../../../../fixtures/default-fixture';
import { EnrollmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
import { EnrollType } from '../../../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let uniqueEnrollmentId: string;
let enrollment: any;

test.beforeAll(async ({ configuration, enrollmentsRepo, roundRepo }) => {
  const userTLI = configuration.shareUsers.userCommentTLI;
  const course = configuration.shareCourses.oicAutoApproveCourse;

  uniqueEnrollmentId = uuidv4();
  const date = new Date();
  const expiredAt = new Date();
  expiredAt.setDate(date.getDate() + 30);

  const round = await roundRepo.create({
    id: uuidv4(),
    courseId: course.courseId,
    courseVersionId: course.courseVersions[1].id,
    firstRegistrationDate: date,
    lastRegistrationDate: expiredAt,
    trainingDate: expiredAt,
    createdAt: date,
    updatedAt: date,
  });

  enrollment = await enrollmentsRepo.create({
    id: uniqueEnrollmentId,
    courseId: course.courseId,
    courseVersionId: course.courseVersions[1].id,
    organizationId: userTLI.organizationId,
    userId: userTLI.guid,
    business: EnrollmentsRepo.BUSINESS.b2c,
    status: EnrollmentsRepo.STATUS.inProgress,
    isCountdownArticle: false,
    roundId: round.id,
    completedCourseItem: 0,
    learningProgress: [],
    startedAt: date,
    expiredAt: expiredAt,
    acceptedAt: null,
    createdAt: date,
    updatedAt: date,
    isIdentityVerificationEnabled: true,
    remark: '',
    externalContentType: '',
    enrollType: EnrollType.voluntary,
    customerCode: '',
  });
});

test.beforeEach(async ({ configuration, loginPage }) => {
  const adminTLI = configuration.shareUsers.userAdminReplyTLI;

  await loginPage.fillUsername(adminTLI.username);
  await loginPage.fillPassword(adminTLI.password);
  await loginPage.submit();
});

test.describe('Document Management - Enrollment Detail', () => {
  test('@SKL-T20021 Admin ทำการขอเอกสารเพิ่มเติมจากผู้เรียนที่มีสถานะกำลังเรียน, ผู้เรียนได้รับแจ้งเตือนบนระบบการขอเอกสาร', async ({
    configuration,
    adminDashboardPage,
    trainingEnrollmentsPage,
    additionalDocumentsPage,
    testmailAppClient,
  }) => {
    const learnerTLI = configuration.shareUsers.userCommentTLI;
    const course = configuration.shareCourses.oicAutoApproveCourse;
    const resetTimestamp = new Date().getTime();

    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsersForRequestDocuments(learnerTLI.firstname + ' ' + learnerTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetail();

    // Access enrollment detail and verify status
    await trainingEnrollmentsPage.viewAdditionalDocumentDetail();
    // verify status

    // Request additional document
    await trainingEnrollmentsPage.requestDocument('ขาดเอกสารบัตรประชาชนยืนยันตน', new Date());

    // Verify learner receives email notification
    const email = await testmailAppClient.fetchLatestEmailInbox(
      learnerTLI.email,
      '*ขอเอกสารเพิ่มเติมสำหรับหลักสูตร*',
      resetTimestamp,
    );
    expect(email.html).toContain('ขอเอกสารเพิ่มเติม');
    expect(email.html).toContain(course.courseVersions[1].name);

    // Verify document request appears in system
    await adminDashboardPage.accessManageAdditionalDocuments();
    await additionalDocumentsPage.viewUserAdditionalDocumentDetail(learnerTLI.firstname + ' ' + learnerTLI.lastname);
    await expect(additionalDocumentsPage.toastMessageSuccessLocator).toBeVisible();
  });

  test('@SKL-T20022 Admin ทำการแก้ไขหมายเหตุ, แสดงข้อมูลที่แก้ไขได้ถูกต้อง', async ({
    configuration,
    adminDashboardPage,
    trainingEnrollmentsPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    page,
  }) => {});

  test('@SKL-T20023 Admin ทำการลบและอัพโหลดรูปภาพยืนยันตัวตน, แสดงข้อมูลที่แก้ไขได้ถูกต้อง', async ({
    configuration,
    adminDashboardPage,
    trainingEnrollmentsPage,
    additionalDocumentsPage,
    enrollmentsRepo,
    page,
  }) => {
    test('@SKL-T20024 Admin ทำการอัพโหลดรูปภาพยืนยันตัวตนที่ไม่ใช่รูปบัตรประชาชน, แสดง Error แจ้งเตือน', async ({
      configuration,
      adminDashboardPage,
      trainingEnrollmentsPage,
      additionalDocumentsPage,
      enrollmentsRepo,
      page,
    }) => {});
  });
});
