import path from 'path';
import * as v from 'valibot';
import { connect } from 'mongoose';
import { test as base } from '@playwright/test';
import * as appSettings from '../configurations/app-settings.json';
import { Configuration } from '../services/configuration';
import { SectionsService } from '../services/sections-service';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { CoursesBySectionsService } from '../services/courses-by-section-service';
import { CoursesRepo } from '../../shared/repositories/b2c/courses-repo';
import { MySQLConnector } from '../../shared/repositories/mysql-base/mysql.connector.sql';
import { CategoryMenuService } from '../services/category-menu-service';
import { CourseCPDService } from '../services/course-cpd-service';
import { CourseService } from '../services/course-service';
import { CategoryBySlugService } from '../services/category-by-slug-service';
import { SectionByCategoryService } from '../services/section-by-category-service';
import { CategoryPageFilterService } from '../services/category-page-filter-service';
import { BundleService } from '../services/bundle-service';
import { UserService } from '../services/user-service';
import { ReviewService } from '../services/review-service';
import { ReviewsRepo } from '../../shared/repositories/b2c/reviews-repo';
import { AppService } from '../services/app-service';
import { WishlistService } from '../services/wishlist-service';
import { UserIdentityService } from '../services/user-service-dentity';
import { ManageIdentityService } from '../services/manage-service-dentity';
import { UserDocumentIdentitiesRepo } from '../../shared/repositories/b2c/user_document_identities-repo';
import { WishlistRepo } from '../../shared/repositories/b2c/wishlist-repo';
import { UserIdentitysService } from '../services/user-identities';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const fs = require('fs');
const APP_SETTING_LOCAL_PATH = '../configurations/app-settings.local.json';

interface TestFixtures {
  configuration: Configuration;
  mySQLConnector: MySQLConnector;
  // Repository
  mongoose: typeof import('mongoose');
  coursesRepo: CoursesRepo;
  reviewsRepo: ReviewsRepo;
  userDocumentIdentitiesRepo: UserDocumentIdentitiesRepo;
  wishlistsRepo: WishlistRepo;

  // API
  apiContextManager: APIContextManager;
  sectionsService: SectionsService;
  coursesBySectionsService: CoursesBySectionsService;
  categoryMenuService: CategoryMenuService;
  courseCPDService: CourseCPDService;
  courseService: CourseService;
  categoryBySlugService: CategoryBySlugService;
  sectionByCategoryService: SectionByCategoryService;
  categoryPageFilterService: CategoryPageFilterService;
  bundleService: BundleService;
  userService: UserService;
  reviewService: ReviewService;
  appService: AppService;
  wishlistService: WishlistService;
  userIdentityService: UserIdentityService;
  manageIdentityService: ManageIdentityService;
  userIdentitysService: UserIdentitysService;
}


const test = base.extend<TestFixtures>({
  // eslint-disable-next-line no-empty-pattern
  configuration: async ({ }, use, context) => {
    let settings = appSettings;
    if (fs.existsSync(path.resolve(__dirname, APP_SETTING_LOCAL_PATH))) {
      const dataObject = JSON.parse(fs.readFileSync(path.resolve(__dirname, APP_SETTING_LOCAL_PATH)));
      settings = { ...settings, ...dataObject };
    }
    const configuration = new Configuration(settings);
    process.env.Timezone = 'Asia/Bangkok';
    context.setTimeout(configuration.appSettings.timeout);
    await use(configuration);
  },
  mySQLConnector: async ({ configuration }, use) => {
    const mySQLConnector = new MySQLConnector({
      MYSQL_HOST: configuration.appSettings.MYSQL_HOST,
      MYSQL_USER: configuration.appSettings.MYSQL_USER,
      MYSQL_PASSWORD: configuration.appSettings.MYSQL_PASSWORD,
      MYSQL_DATABASE: configuration.appSettings.MYSQL_DATABASE,
    });
    await use(mySQLConnector);
  },
  mongoose: async ({ configuration }, use) => {
    const mongoose = await connect(configuration.appSettings.DB_HOST);
    await use(mongoose);
  },
  coursesRepo: async ({ mySQLConnector }, use) => {
    const coursesRepo = new CoursesRepo(mySQLConnector);
    await use(coursesRepo);
  },
  reviewsRepo: async ({ mySQLConnector }, use) => {
    const reviewsRepo = new ReviewsRepo(mySQLConnector);
    await use(reviewsRepo)
  },
  userDocumentIdentitiesRepo: async ({ mySQLConnector }, use) => {
    const userDocumentIdentitiesRepo = new UserDocumentIdentitiesRepo(mySQLConnector);
    await use(userDocumentIdentitiesRepo)
  },
  wishlistsRepo: async ({ mySQLConnector }, use) => {
    const wishlistsRepo = new WishlistRepo(mySQLConnector);
    await use(wishlistsRepo)
  },
  apiContextManager: ({ playwright, request }, use) => {
    const apiContextManager = new APIContextManager(playwright.request, request);
    use(apiContextManager);
  },
  sectionsService: ({ apiContextManager, configuration }, use) => {
    const sectionsService = new SectionsService(apiContextManager, configuration);
    use(sectionsService);
  },
  coursesBySectionsService: ({ apiContextManager, configuration }, use) => {
    const coursesBySectionsService = new CoursesBySectionsService(apiContextManager, configuration);
    use(coursesBySectionsService);
  },
  categoryMenuService: ({ apiContextManager, configuration }, use) => {
    const categoryMenuService = new CategoryMenuService(apiContextManager, configuration);
    use(categoryMenuService);
  },
  courseCPDService: ({ apiContextManager, configuration }, use) => {
    const courseCPDService = new CourseCPDService(apiContextManager, configuration);
    use(courseCPDService);
  },
  courseService: ({ apiContextManager, configuration }, use) => {
    const courseService = new CourseService(apiContextManager, configuration);
    use(courseService);
  },
  categoryBySlugService: ({ apiContextManager, configuration }, use) => {
    const categoryBySlugService = new CategoryBySlugService(apiContextManager, configuration);
    use(categoryBySlugService);
  },
  sectionByCategoryService: ({ apiContextManager, configuration }, use) => {
    const sectionByCategoryService = new SectionByCategoryService(apiContextManager, configuration);
    use(sectionByCategoryService);
  },
  categoryPageFilterService: ({ apiContextManager, configuration }, use) => {
    const categoryPageFilterService = new CategoryPageFilterService(apiContextManager, configuration);
    use(categoryPageFilterService);
  },
  bundleService: ({ apiContextManager, configuration }, use) => {
    const bundleService = new BundleService(apiContextManager, configuration);
    use(bundleService);
  },
  userService: ({ apiContextManager, configuration }, use) => {
    const userService = new UserService(apiContextManager, configuration, configuration.appSettings.baseUrl);
    use(userService);
  },
  reviewService: ({ apiContextManager, configuration }, use) => {
    const reviewService = new ReviewService(apiContextManager, configuration);
    use(reviewService);
  },
  appService: ({ apiContextManager, configuration }, use) => {
    const appService = new AppService(apiContextManager, configuration);
    use(appService);
  },
  wishlistService: ({ apiContextManager, configuration }, use) => {
    const wishlistService = new WishlistService(apiContextManager, configuration);
    use(wishlistService);
  },

  userIdentityService: ({ apiContextManager, configuration }, use) => {
    const userIdentityService = new UserIdentityService(apiContextManager, configuration, configuration.appSettings.baseUrl);
    use(userIdentityService);
  },
  manageIdentityService: ({ apiContextManager, configuration }, use) => {
    const manageIdentityService = new ManageIdentityService(apiContextManager, configuration, configuration.appSettings.baseUrl);
    use(manageIdentityService);
  },
  userIdentitysService: ({ apiContextManager, configuration }, use) => {
    const userIdentitysService = new UserIdentitysService(apiContextManager, configuration);
    use(userIdentitysService);
  }
});

export { test , v };
