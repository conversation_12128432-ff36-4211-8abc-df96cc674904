import { expect } from '@playwright/test';
import { test, v } from '../../../fixtures/default-fixture';

test.describe('Positive', () => {
    test('User able to get course SEO details with correct data format', async ({ userService , courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseSeo(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const Schema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                id: v.number(),
                idName: v.string(),
                name: v.string(),
                description: v.string(),
                seoDescription: v.string(),
                seoEnable: v.boolean(),
                commercialMessage: v.union([v.string(), v.null()]),
                shareThumbnailUrl: v.string(),
                fbContent: v.string(),
                instructors: v.array(
                    v.object({
                        fullName: v.string(),
                        biology: v.string(),
                        slug: v.string(),
                    })
                ),
                productLearningTypeId: v.number(),
            }),
            timestamp: v.number(),
        });

        v.parse(Schema, respJson);
    });

    test('User able to get extended course details with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseDetails(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const ExtendedSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                price: v.object({
                    regular: v.object({
                        sku: v.string(),
                        fullPrice: v.number(),
                        salePrice: v.number(),
                    }),
                }),
                gtmContentId: v.string(),
                gtmProductBrand: v.string(),
                productAffiliation: v.string(),
                highlightImage2xUrl: v.string(),
                highlightImage1xUrl: v.string(),
                hasCertificate: v.boolean(),
                useVideoPreviewCourseBanner: v.boolean(),
                videoCount: v.number(),
                totalVideoDuration: v.number(),
                countdownDay: v.number(),
                attachmentFileCount: v.number(),
                quizCount: v.number(),
                assignmentCount: v.number(),
                examinationCount: v.number(),
                articleCount: v.number(),
                isUseMailTemplate: v.boolean(),
                mailContent: v.string(),
                mailSubject: v.string(),
                tweeterContent: v.union([v.string(), v.null()]),
                mainCategory: v.string(),
                subCategory: v.string(),
                bannerPrimaryColor: v.string(),
                bannerSecondaryColor: v.string(),
                hasCertificateBundle: v.boolean(),
            }),
            timestamp: v.number(),
        });

        v.parse(ExtendedSchema, respJson);
    });

    test('User able to get course glances with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseGlances(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const GlancesSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                glances: v.array(
                    v.object({
                        name: v.string(),
                    })
                ),
            }),
            timestamp: v.number(),
        });

        v.parse(GlancesSchema, respJson);
    });

    test('User able to get course curriculum with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseCurriculum(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const CurriculumSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.array(
                v.object({
                    partId: v.number(),
                    partName: v.string(),
                    contents: v.array(
                        v.object({
                            id: v.number(),
                            type: v.string(),
                            name: v.string(),
                            duration: v.number(),
                            isPreview: v.boolean(),
                            attachmentCount: v.number(),
                        })
                    ),
                })
            ),
            timestamp: v.number(),
        });

        v.parse(CurriculumSchema, respJson);
    });

    test('User able to get course video preview with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseVideoPreview(course.courseID, course.videoChapterID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const VideoPreviewSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                videoUrl: v.string(),
                subtitles: v.array(v.any()), // Adjust schema for subtitles if needed
            }),
            timestamp: v.number(),
        });

        v.parse(VideoPreviewSchema, respJson);
    });

    test('User able to get personal course review with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseReviewMe(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const ReviewMeSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                userId: v.number(),
                userAvartar: v.string(),
                reviewerName: v.string(),
                rating: v.number(),
                comment: v.string(),
                updatedAt: v.string(),
            }),
            timestamp: v.number(),
        });

        v.parse(ReviewMeSchema, respJson);
    });

    test('User able to get course discussions with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseDiscussions(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const DiscussionsSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                items: v.array(
                    v.object({
                        id: v.number(),
                        title: v.string(),
                        content: v.union([v.string(), v.null()]),
                        createdAt: v.string(),
                        discussionReplies: v.array(
                            v.object({
                                id: v.number(),
                                content: v.string(),
                                createdAt: v.string(),
                                discussionId: v.number(),
                                user: v.object({
                                    id: v.number(),
                                    fullname: v.string(),
                                    imageUrl: v.string(),
                                    isAuthor: v.boolean(),
                                }),
                            })
                        ),
                        user: v.object({
                            id: v.number(),
                            fullname: v.string(),
                            imageUrl: v.string(),
                            isAuthor: v.boolean(),
                        }),
                    })
                ),
                page: v.number(),
                pageSize: v.number(),
                itemCount: v.number(),
                pageCount: v.number(),
                hasPreviousPage: v.boolean(),
                hasNextPage: v.boolean(),
                hasPagination: v.boolean(),
            }),
            timestamp: v.number(),
        });

        v.parse(DiscussionsSchema, respJson);
    });

    test('User able to get course discussion details with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseDiscussionDetails(course.courseID, course.discussionId);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const DiscussionDetailsSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                id: v.number(),
                title: v.string(),
                content: v.union([v.string(), v.null()]),
                createdAt: v.string(),
                discussionReplies: v.array(
                    v.object({
                        id: v.number(),
                        content: v.string(),
                        createdAt: v.string(),
                        discussionId: v.number(),
                        user: v.object({
                            id: v.number(),
                            fullname: v.string(),
                            imageUrl: v.string(),
                            isAuthor: v.boolean(),
                        }),
                    })
                ),
                user: v.object({
                    id: v.number(),
                    fullname: v.string(),
                    imageUrl: v.string(),
                    isAuthor: v.boolean(),
                }),
            }),
            timestamp: v.number(),
        });

        v.parse(DiscussionDetailsSchema, respJson);
    });

    test('User able to get course discussion replies with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseDiscussionReplies(course.courseID, course.discussionId);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const DiscussionRepliesSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.array(
                v.object({
                    id: v.number(),
                    content: v.string(),
                    createdAt: v.string(),
                    discussionId: v.number(),
                    user: v.object({
                        id: v.number(),
                        fullname: v.string(),
                        imageUrl: v.string(),
                        isAuthor: v.boolean(),
                    }),
                })
            ),
            timestamp: v.number(),
        });

        v.parse(DiscussionRepliesSchema, respJson);
    });

    test('User able to get course suggestions with correct data format', async ({ userService, courseService, configuration }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);

        const resp = await courseService.getCourseSuggestions(course.courseID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const SuggestionsSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                courses: v.array(
                    v.object({
                        id: v.number(),
                        name: v.string(),
                        id_name: v.string(),
                        duration: v.number(),
                        video_amount: v.number(),
                        full_price: v.number(),
                        price: v.number(),
                        published_at: v.string(),
                        cpd_enabled: v.boolean(),
                        mail_subject: v.string(),
                        mail_content: v.string(),
                        fb_content: v.union([v.string(), v.null()]),
                        fb_title: v.union([v.string(), v.null()]),
                        tw_content: v.union([v.string(), v.null()]),
                        seoDescription: v.union([v.string(), v.null()]),
                        countdown_day: v.number(),
                        categories_id: v.union([v.string(), v.null()]),
                        have_certificate: v.union([v.boolean(), v.null()]),
                        instructors_info: v.array(
                            v.object({
                                firstname: v.string(),
                                lastname: v.string(),
                                highlight_desc: v.union([v.string(), v.null()]),
                                image_url: v.union([v.string(), v.null()]),
                                slug: v.string(),
                                biology: v.union([v.string(), v.null()]),
                            })
                        ),
                        productAffiliation: v.string(),
                    })
                ),
            }),
            timestamp: v.number(),
        });

        v.parse(SuggestionsSchema, respJson);
    });

    test('User able to search course learning path with correct data format', async ({ configuration , userService, courseService }) => {
        const course = configuration.course.courses;
        const user = configuration.course.users01;
        await userService.loginAndSetToken(user);
        const resp = await courseService.searchCourseLearningPath();
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const SearchLearningPathSchema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                items: v.array(
                    v.object({
                        id: v.number(),
                        name: v.string(),
                        instructors: v.array(
                            v.object({
                                id: v.number(),
                                fullName: v.string(),
                                officialFirstname: v.union([v.string(), v.null()]),
                                officialLastname: v.union([v.string(), v.null()]),
                                officialTitle: v.union([v.string(), v.null()]),
                                officialPrefixTh: v.union([v.string(), v.null()]),
                                officialPrefixEn: v.union([v.string(), v.null()]),
                            })
                        ),
                        category: v.array(v.string()),
                        subcategory: v.array(v.string()),
                        isTu: v.boolean(),
                    })
                ),
                page: v.number(),
                pageSize: v.number(),
                itemCount: v.number(),
                pageCount: v.number(),
                hasPreviousPage: v.boolean(),
                hasNextPage: v.boolean(),
                hasPagination: v.boolean(),
            }),
            timestamp: v.number(),
        });

        v.parse(SearchLearningPathSchema, respJson);
    });
})