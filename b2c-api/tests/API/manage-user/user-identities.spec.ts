import { expect } from '@playwright/test';
import { test, v } from '../../../fixtures/default-fixture';

test.describe('Positive', () => {

    test('User able to get user identities with correct data format', async ({ userIdentitysService, userService, configuration }) => {
        const user = configuration.userIdentity.users01;
        await userService.loginAndSetToken(user);

        const resp = await userIdentitysService.getUserIdentities();
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const Schema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                items: v.array(
                    v.object({
                        id: v.number(),
                        email: v.string(),
                        citizenId: v.union([v.string(), v.null()]),
                        createdAt: v.string(),
                        passwordEnabled: v.boolean(),
                        provider: v.string(),
                        fullName: v.string(),
                        officialFullName: v.string(),
                        userType: v.string(),
                    })
                ),
                page: v.number(),
                pageSize: v.number(),
                itemCount: v.number(),
                pageCount: v.number(),
                hasPreviousPage: v.boolean(),
                hasNextPage: v.boolean(),
                hasPagination: v.boolean(),
            }),
            timestamp: v.number(),
        });

        v.parse(Schema, respJson);
    });

    test('User able to get course identity logs with correct data format', async ({ userIdentitysService, userService, configuration }) => {
        const user = configuration.userIdentity.users01;
        await userService.loginAndSetToken(user);

        const resp = await userIdentitysService.getCourseIdentityLogs(user.userID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const Schema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                items: v.array(v.unknown()), // Adjust schema if items have a specific structure
                page: v.number(),
                pageSize: v.number(),
                itemCount: v.number(),
                pageCount: v.number(),
                hasPreviousPage: v.boolean(),
                hasNextPage: v.boolean(),
                hasPagination: v.boolean(),
            }),
            timestamp: v.number(),
        });

        v.parse(Schema, respJson);
    });

    test('User able to get identity log by face comparison ID with correct data format', async ({ userIdentitysService, userService, configuration }) => {
        const user = configuration.userIdentity.users01;
        await userService.loginAndSetToken(user);

        const resp = await userIdentitysService.getIdentityLogByFaceComparisonId(user.userFaceComparisonID);
        const respJson = await resp.json();

        await expect(resp).toBeOK();
        const Schema = v.object({
            status: v.literal('000'),
            message: v.literal('Success'),
            data: v.object({
                percentSimilarity: v.number(),
                createdAt: v.string(),
                comparisonStatus: v.string(),
                userIdentityDocumentInfo: v.object({
                    userDocumentIdentityId: v.number(),
                    userIdentityImageValidationId: v.number(),
                    officialFirstNameTH: v.string(),
                    officialLastNameTH: v.string(),
                    officialFirstNameEN: v.string(),
                    officialLastNameEN: v.string(),
                    citizenId: v.string(),
                    birthDate: v.string(),
                }),
                userFaceIdentity: v.object({
                    targetUserIdentityImageValidationId: v.number(),
                    officialFirstNameTH: v.string(),
                    officialLastNameTH: v.string(),
                    officialFirstNameEN: v.string(),
                    officialLastNameEN: v.string(),
                    citizenId: v.string(),
                    birthDate: v.string(),
                }),
            }),
            timestamp: v.number(),
        });

        v.parse(Schema, respJson);
    });
})