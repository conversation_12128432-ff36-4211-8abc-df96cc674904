import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export enum COURSE_CPD_SERVICE {
  license0to3 = '0to3',
  published_at = 'published_at',
  licenseRenewal = 'licenseRenewal',
  asc = 'asc',
  desc = 'desc',
}

export class CourseCPDService extends BaseAPIService {
  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/v1/cpd/courses';
  }

  async getCourseCPD(
    citizenId: string,
    regulatorType: string,
    licenseType: string,
    applicantType: string,
    licenseRenewal: string,
    sortBy: string,
    orderBy: string,
  ): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}`, {
      headers: this.getHeaderForBaseUrl(),
      params: {
        citizenId: citizenId,
        regulatorType: regulatorType,
        licenseType: licenseType,
        applicantType: applicantType,
        licenseRenewal: licenseRenewal,
        sortBy: sortBy,
        orderBy: orderBy,
      },
    });
    return resp;
  }
}
