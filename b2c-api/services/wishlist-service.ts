import { APIResponse, chromium, B<PERSON>er, BrowserContext } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class WishlistService extends BaseAPIService {
  readonly endpoint: string;
  readonly endpointGetWishlistByCourseId: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);

    this.endpoint = '/v1/users/me/wishlist';
    this.endpointGetWishlistByCourseId = '/v1/users/me/wishlist/courses';
  }

  async getMyWishlistCourses(withToken = true): Promise<APIResponse> {
    const getMyWishlistCourse = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return getMyWishlistCourse;
  }

  async addCoursesToWishlistByCourseId(courseId: number, withToken = true): Promise<APIResponse> {
    const addCoursesToWishlist = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        courseId: courseId,
      },
    });
    return addCoursesToWishlist;
  }

  async removeCoursesFromWishlistByCourseId(courseId: number, withToken = true): Promise<APIResponse> {
    const removeCoursesFromWishlist = await this.contextManager
      .getContext()
      .delete(`${this.baseApiUrl}${this.endpoint}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          courseId: courseId,
        },
      });
    return removeCoursesFromWishlist;
  }

  async getMyWihlistByCourseId(courseId: number, withToken = true): Promise<APIResponse> {
    const getMyWihlistByCourseId = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpointGetWishlistByCourseId}/${courseId}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          courseId: courseId,
        },
      });
    return getMyWihlistByCourseId;
  }
}
