import { APIResponse, chromium, <PERSON><PERSON><PERSON>, BrowserContext } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class UserIdentitysService extends BaseAPIService {

  readonly endpointUserIdentities: string;
  readonly endpointCourseIdentityLogs: string;
  readonly endpointIdentityLogs: string;
  readonly endpointIdentityLogsSummary: string;
  readonly endpointIdentityLogByFaceComparisonId: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);

    this.endpoint = '/v1/manage/users';
    this.endpointUserIdentities = '/user-identities';
    this.endpointCourseIdentityLogs = '/course-identity-logs'; //user-identities/{userId}/course-identity-logs
    this.endpointIdentityLogs = '/identity-logs'; //{userId}/identity-logs
    this.endpointIdentityLogsSummary = '/identity-logs/summary'; //{userId}/identity-logs/summary
    this.endpointIdentityLogByFaceComparisonId = '/identity-log'; //identity-log/{userFaceComparisonId}
  }

  async getUserIdentities(withToken = true): Promise<APIResponse> {
    const response = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}${this.endpointUserIdentities}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return response;
  }

  async getCourseIdentityLogs(userId: string, withToken = true): Promise<APIResponse> {
    const response = await this.contextManager
      .getContext()
      .get(
        `${this.baseApiUrl}${this.endpoint}${this.endpointUserIdentities}/${userId}${this.endpointCourseIdentityLogs}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
        }
      );
    return response;
  }

  async getIdentityLogs(userId: string, withToken = true): Promise<APIResponse> {
    const response = await this.contextManager
      .getContext()
      .get(
        `${this.baseApiUrl}${this.endpoint}/${userId}${this.endpointIdentityLogs}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
        }
      );
    return response;
  }

  async getIdentityLogByFaceComparisonId(userFaceComparisonId: number, withToken = true): Promise<APIResponse> {
    const response = await this.contextManager
      .getContext()
      .get(
        `${this.baseApiUrl}${this.endpoint}${this.endpointIdentityLogByFaceComparisonId}/${userFaceComparisonId}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
        }
      );
    return response;
  }
}