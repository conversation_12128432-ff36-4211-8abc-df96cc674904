import jwt from 'jsonwebtoken';
import { APIResponse, chromium, Browser, BrowserContext } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';
import { LoginPage } from '../../b2c-e2e/elements/pages/regular-site/login-page';
import { HomePage } from '../../b2c-e2e/elements/pages/regular-site/home-page';
import path from 'path';
import fs from 'fs';
import { StringExpression } from 'mongoose';


export class ManageIdentityService extends BaseAPIService {
  private readonly _url: string;

  readonly dataEndPoint: string;
  // readonly endpointIdentityVerifications: string;
  readonly endpointIdentityExtractions: string;
  readonly endpointUserIdentity: string;
  readonly endpointCourseIdentityLogs: string;
  // readonly endpointIdentityInformationValidation: string;
  // readonly endpointFaceCompareValidations: string;
  readonly endpointIdentifyValidation: string;
  // readonly endpointDecryptionImage: string;
  readonly endpointIdentityInformation: string;
  readonly endpointDeleteIdentityInformationIdentityFace: string;
  readonly endpointDeleteIdentityInformationIdentityDocument: string;
  readonly endpointUpdateIdentityInformationIdentityDocument: string;
  readonly endpointGetIdentityInformationIdentityFace: string;
  readonly endpointIdentityImageValidationIdDecryptionImage: string;
  readonly endpointIdentityLogsSummary: string;
  readonly endpointIdentityLogsFaceCompare: string;

  constructor(contextManager: APIContextManager, configuration: Configuration, url: string) {
    super(contextManager, configuration);
    this._url = url
    this.endpoint = '/v1/manage/users';
    // this.endpointIdentityVerifications = '/identity-verifications';
    this.endpointIdentityExtractions = '/identity-extractions';
    // this.endpointIdentityInformationValidation = '/identity-information-validation';
    // this.endpointFaceCompareValidations = '/face-comparisons';
    this.endpointIdentifyValidation = '/identity-verifications';
    // this.endpointDecryptionImage = '/decryption-image';
    this.endpointIdentityInformation = '/identity-informations';
    this.endpointDeleteIdentityInformationIdentityFace = '/identity-information/identity-face/';
    this.endpointDeleteIdentityInformationIdentityDocument = '/identity-information/identity-document';
    this.endpointUpdateIdentityInformationIdentityDocument = '/identity-information/identity-document';
    this.endpointGetIdentityInformationIdentityFace = '/identity-information/identity-face';
    this.endpointIdentityImageValidationIdDecryptionImage = '/decryption-image'
    this.endpointUserIdentity = '/user-identities';
    this.endpointCourseIdentityLogs = '/course-identity-logs';
    this.endpointIdentityLogsSummary = '/identity-logs/summary';
    this.endpointIdentityLogsFaceCompare = '/identity-log';

  }

  async readAccessToken(filePath: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const parsedData = JSON.parse(data);
          const access_token = parsedData.cookies.find((cookie) => cookie.name === 'access_token');

          if (access_token) {
            resolve(access_token.value);
          } else {
            reject(new Error('Access token not found in JSON data.'));
          }
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  async isTokenExpired(token: string): Promise<boolean> {
    try {
      const decodedToken: any = jwt.decode(token);

      if (!decodedToken || !decodedToken.exp) {
        return true;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return decodedToken.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  async getTokenByEmailViaWebBrowser(email: string, password: string, reLogin = false): Promise<string> {

    // Check if the storage state file exists.
    const exists = fs.existsSync('storageState.json');

    if (exists && reLogin == false) {
      const token = await this.readAccessToken('storageState.json')
      const tokenStatus = await this.isTokenExpired(token)
      if (token != "" && tokenStatus == false) {
        this.contextManager.setAccessToken(token);
        return token;
      }
    }
    const browser: Browser = await chromium.launch();
    const context: BrowserContext = await browser.newContext();
    const page = await context.newPage();

    await page.goto(this._url);

    const homePage = new HomePage(page);
    await homePage.acceptPdpa();
    await homePage.navbar.accessLoginPage();
    const loginPage = new LoginPage(page);
    const token = await loginPage.getTokenByEmail(email, password);

    await page.context().storageState({ path: 'storageState.json' });

    await context.close();
    this.contextManager.setAccessToken(token);
    return token;
  }

  async getUsersImageFile(fileName: string): Promise<fs.ReadStream> {
    const imgPath = path.resolve(__dirname, "../test-data/users", fileName);
    return fs.createReadStream(imgPath);
  }


  async identityinformationByUserId(userid: string, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}/${userid}${this.endpointIdentityInformation}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async identityExtractions(imageValidationId: any, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentityExtractions}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        userIdentityImageValidationId: imageValidationId,
      }
    });
    return resp;
  }

  async userIdentity(withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointUserIdentity}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async courseIdentityLogs(userID: string, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointUserIdentity}/${userID}${this.endpointCourseIdentityLogs}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async getIdentityLogsSummary(userID: string, quizId: number, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}/${userID}${this.endpointIdentityLogsSummary}?quizId=${quizId}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async getIdentityLogsFaceCompare(userFaceComparisonId: number, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentityLogsFaceCompare}/${userFaceComparisonId}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async deleteIdentityInformationIdentityFace(userIdentityFaceId: any, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().delete(`${this.baseApiUrl}${this.endpoint}${this.endpointDeleteIdentityInformationIdentityFace}${userIdentityFaceId}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async userIdentityImageValidationIdDecryptionImage(userID: string, userIdentityImageValidationId: number, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}/${userID}/user-identity-image-validation/${userIdentityImageValidationId}${this.endpointIdentityImageValidationIdDecryptionImage}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async getIdentityInformationIdentityFace(userId: string, userIdentityImageValidationId: number, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointGetIdentityInformationIdentityFace}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        userId: Number(userId),
        userIdentityImageValidationId: userIdentityImageValidationId
      }
    });
    return resp;
  }

  async deleteIdentityInformationIdentityDocument(userIdentityDocumentId: any, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().delete(`${this.baseApiUrl}${this.endpoint}${this.endpointDeleteIdentityInformationIdentityDocument}/${userIdentityDocumentId}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async updateIdentityInformationDocument(
    userDocumentIdentityId: number,
    citizenId: string,
    firstnameTh: string,
    lastnameTh: string,
    firstnameEn: string,
    lastnameEn: string,
    birthDate: string,
    withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().put(`${this.baseApiUrl}${this.endpoint}${this.endpointUpdateIdentityInformationIdentityDocument}/${userDocumentIdentityId}`, {
      headers: this.getHeaderForBaseUrl(withToken),

      data: {
        userDocumentIdentityId: userDocumentIdentityId,
        citizenId: citizenId,
        firstnameTh: firstnameTh,
        lastnameTh: lastnameTh,
        firstnameEn: firstnameEn,
        lastnameEn: lastnameEn,
        birthDate: birthDate
      }
    });
    return resp;
  }

  async identityVerifications(img: any, imageType: string, userID: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentifyValidation}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      multipart: {
        image: img,
        imageType: imageType,
        userId: userID
      }
    })
    return resp;
  }
}