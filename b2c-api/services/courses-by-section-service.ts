import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export enum COURSE_SECTION_SERVICE {
  popular = 'popular',
  latest = 'latest',
  freePopular = 'free-popular',
}

export class CoursesBySectionsService extends BaseAPIService {
  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/v1/courses/sections';
  }

  async getCoursesBySections(sections: string, offset: string, limit: string): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}/${sections}`, {
      headers: this.getHeaderForBaseUrl(),
      params: {
        offset: offset,
        limit: limit,
      },
    });
    return resp;
  }
}
