import jwt from 'jsonwebtoken';
import { APIResponse, chromium, Browser, BrowserContext } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';
import { LoginPage } from '../../b2c-e2e/elements/pages/regular-site/login-page';
import { HomePage } from '../../b2c-e2e/elements/pages/regular-site/home-page';
import path from 'path';
import fs, { read } from 'fs';
import qs from 'qs';


export class UserService extends BaseAPIService {
  private readonly _url: string;

  readonly dataEndPoint: string;
  readonly endpointv2: string;
  readonly endpointUserMe: string;
  readonly endpointUserCredentials: string;
  readonly endpointCheckEmailDuplicate: string;
  readonly endpointSyncProfile: string;
  readonly endpointIdsProfile: string;
  readonly baseHeaders: Record<string, string>;
  readonly endpointCheckCitizenIdDuplicate: string;
  readonly endpointCountries: string;
  readonly endpointNotifications: string;

  constructor(contextManager: APIContextManager, configuration: Configuration, url: string) {
    super(contextManager, configuration);
    this._url = url;
    this.baseHeaders = {
      'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'en-US,en;q=0.9',
      'Content-Security-Policy': 'frame-ancestors pre-prod-web-b2c.skilllane.net pre-prod-account.skilllane.net'
    };
    this.endpoint = '/v1/users';
    this.endpointv2 = '/v2/users';
    this.endpointUserMe = '/me';
    this.endpointUserCredentials = '/me/credentials';
    this.endpointCheckEmailDuplicate = '/check-email-duplicate?email=';
    this.endpointSyncProfile = '/sync-profile';
    this.endpointIdsProfile = '/ids-profile';
    this.endpointCheckCitizenIdDuplicate = '/check-citizen-id-duplicate';
    this.endpointCountries = '/countries';
    this.endpointNotifications = '/v1/notifications';
  }

  async readAccessToken(filePath: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const parsedData = JSON.parse(data);
          const access_token = parsedData.cookies.find((cookie) => cookie.name === 'access_token');

          if (access_token) {
            resolve(access_token.value);
          } else {
            reject(new Error('Access token not found in JSON data.'));
          }
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  async isTokenExpired(token: string): Promise<boolean> {
    try {
      const decodedToken: any = jwt.decode(token);

      if (!decodedToken || !decodedToken.exp) {
        return true;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return decodedToken.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

async getAccessTokenByUsernamePassword(username, password) {
  let url = this.baseUrl+"/user/auth/skilllane?acr_values=login";
  let response = await this.contextManager.getContext().get(url, { headers: this.baseHeaders, maxRedirects: 0 });

  url = response.headers()['location']?.replace("http:", "https:");
  if (!url) throw new Error("Redirect location missing");
  response = await this.contextManager.getContext().get(url, { headers: this.baseHeaders, maxRedirects: 0 });

  url = response.headers()['location']?.replace("http:", "https:");
  if (!url) throw new Error("Redirect location missing");
  response = await this.contextManager.getContext().get(url, { headers: this.baseHeaders, maxRedirects: 0 });

  const urlCookie = response.headersArray()[6].value;
  const cookies = response.headersArray()[7].value;
  const antiforgeryKey = cookies.match(/.AspNetCore.Antiforgery.DHYgknVHjfE=([^;]+)/)[1];
  const previousUrl = urlCookie.match(/previousUrl=([^;]+)/)[1];
  url = this.baseIDSUrl + previousUrl;
  let decodedOnce = decodeURIComponent(url);

  let data = qs.stringify({
      'IsValid': 'true',
      'Username': username,
      'Password': password,
      '__RequestVerificationToken': antiforgeryKey
  });
  response = await this.contextManager.getContext().post(decodedOnce, {
      headers: { 
          'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'accept-encoding': 'gzip, deflate, br, zstd',
          'accept-language': 'en-US,en;q=0.9',
          'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: data,
      maxRedirects: 0
  });

  url = this.baseIDSUrl + response.headers()['location'];
  response = await this.contextManager.getContext().get(url, { headers: this.baseHeaders, maxRedirects: 0 });

  url = previousUrl.replace("/Account/Login?ReturnUrl=", "");
  url = this.baseIDSUrl + decodeURIComponent(url);
  response = await this.contextManager.getContext().get(url, { headers: this.baseHeaders, maxRedirects: 0 });

  url = this.baseIDSUrl + (response.headers()['location']?.replace("http:", "https:"));
  if (!url) throw new Error("Redirect location missing");
  response = await this.contextManager.getContext().get(url, { headers: this.baseHeaders, maxRedirects: 0 });

  url = response.headers()['location'];
  response = await this.contextManager.getContext().get(url, {
      headers: this.baseHeaders,
      maxRedirects: 0
  });

  return JSON.stringify(response.headers()['set-cookie']).match(/access_token=([^;]+)/)[1];
}
    
  async getTokenByEmailViaWebBrowser(email: string, password: string, reLogin = false): Promise<string> {

    // Check if the storage state file exists.
    const exists = fs.existsSync('storageState.json');

    if (exists && reLogin == false) {
      const token = await this.readAccessToken('storageState.json')
      const tokenStatus = await this.isTokenExpired(token)
      if (token != "" && tokenStatus == false) {
        this.contextManager.setAccessToken(token);
        return token;
      }
    }
    const browser: Browser = await chromium.launch();
    const context: BrowserContext = await browser.newContext();
    const page = await context.newPage();

    await page.goto(this._url);

    const homePage = new HomePage(page);
    await homePage.acceptPdpa();
    await homePage.navbar.accessLoginPage();
    const loginPage = new LoginPage(page);
    const token = await loginPage.getTokenByEmail(email, password);

    await page.context().storageState({ path: 'storageState.json' });

    await context.close();
    this.contextManager.setAccessToken(token);
    return token;
  }

  async getUsersImageFile(fileName: string): Promise<fs.ReadStream> {
    const imgPath = path.resolve(__dirname, "../test-data/users", fileName);
    return fs.createReadStream(imgPath);
  }

  async getUsersMe(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointUserMe}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async getUsersCredentials(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointUserCredentials}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async checkEmailDuplicate(email: string, withToken = true): Promise<APIResponse> {
    email = encodeURIComponent(email);
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointCheckEmailDuplicate}${email}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp

  }

  async checkCitizenIdDuplicate(citizenId: string, withToken = true): Promise<APIResponse> {
    citizenId = encodeURIComponent(citizenId);
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointCheckCitizenIdDuplicate}?citizenId=${citizenId}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async getCountries(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpointv2}${this.endpointCountries}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async getIdsProfile(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointIdsProfile}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async getNotifications(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}/v1${this.endpointNotifications}`, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async loginAndSetToken(user): Promise<string>{
      const token = await this.getAccessTokenByUsernamePassword(user.email, user.password);
      await this.setBearerToken(token);
      return token
  }

  async markNotificationAsSeen(notificationId: number, withToken = true): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointNotifications}/${notificationId}/seen`;
    const resp = await this.contextManager.getContext().patch(url, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }

  async markNotificationAsRead(notificationId: number, withToken = true): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointNotifications}/${notificationId}/read`;
    const resp = await this.contextManager.getContext().patch(url, {
      headers: this.getHeaderForBaseUrl(withToken),
    });
    return resp;
  }
}