import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class SectionByCategoryService extends BaseAPIService {
  readonly dataEndPoint: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/v1/categories';
    this.dataEndPoint = 'sections';
  }

  async getCategoryByCategoryId(categoryId: string): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}/${categoryId}/${this.dataEndPoint}`, {
        headers: this.getHeaderForBaseUrl(),
      });
    return resp;
  }
}
