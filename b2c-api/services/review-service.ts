import { APIResponse, chromium, <PERSON><PERSON>er, BrowserContext } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class ReviewService extends BaseAPIService {

  readonly endpointReview: string;
  readonly endpointSkipReview: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);

    this.endpoint = '/v1/courses';
    this.endpointReview = '/reviews';
    this.endpointSkipReview = '/review-skip-log';
  }

  async createOrUpdateReviewByCourseIdOrIdName(courseIdOrIdName: string, score: any, comment: string, withToken = true): Promise<APIResponse> {
    const createOrUpdateReview = await this.contextManager
      .getContext()
      .patch(`${this.baseApiUrl}${this.endpoint}/${courseIdOrIdName}${this.endpointReview}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          rating: score,
          comment: comment
        },
      });
    return createOrUpdateReview;
  }

  async getReviewByCourseIdOrIdName(courseIdOrIdName: string, limit = 1, withToken = true): Promise<APIResponse> {
    const getReview = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}/${courseIdOrIdName}${this.endpointReview}?limit=${limit}`, {
        headers: this.getHeaderForBaseUrl(withToken)
      },
      )
    return getReview;
  }

  async skipReviewLogging(courseIdOrIdName: string, withToken = true): Promise<APIResponse> {
    const skipReviewLog = await this.contextManager
      .getContext()
      .put(`${this.baseApiUrl}${this.endpoint}/${courseIdOrIdName}${this.endpointReview}${this.endpointSkipReview}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return skipReviewLog;
  }
  
  async getReviewSummariesByCourseIdOrIdName(courseIdOrIdName: number, withToken = true): Promise<APIResponse> {
    const reviewSummaries = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpoint}/${courseIdOrIdName}${this.endpointReview}/summaries`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return reviewSummaries;
  }
}