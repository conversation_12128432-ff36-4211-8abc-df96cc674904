import jwt from 'jsonwebtoken';
import { APIResponse, chromium, Browser, BrowserContext } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';
import { LoginPage } from '../../b2c-e2e/elements/pages/regular-site/login-page';
import { HomePage } from '../../b2c-e2e/elements/pages/regular-site/home-page';
import path from 'path';
import fs from 'fs';


export class UserIdentityService extends BaseAPIService {
  private readonly _url: string;

  readonly dataEndPoint: string;
  readonly endpointIdentityVerifications: string;
  readonly endpointIdentityExtractions: string;
  readonly endpointIdentityInformationValidation: string;
  readonly endpointFaceCompareValidations: string;
  readonly endpointUserIdentifyImageValidation: string;
  readonly endpointDecryptionImage: string;
  readonly endpointIdentityInformation: string;

  constructor(contextManager: APIContextManager, configuration: Configuration, url: string) {
    super(contextManager, configuration);
    this._url = url
    this.endpoint = '/v1/users';
    this.endpointIdentityVerifications = '/identity-verifications';
    this.endpointIdentityExtractions = '/identity-extractions';
    this.endpointIdentityInformationValidation = '/identity-information-validation';
    this.endpointFaceCompareValidations = '/face-comparisons';
    this.endpointUserIdentifyImageValidation = '/user-identity-image-validation';
    this.endpointDecryptionImage = '/decryption-image';
    this.endpointIdentityInformation = '/identity-information';


  }

  async readAccessToken(filePath: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const parsedData = JSON.parse(data);
          const access_token = parsedData.cookies.find((cookie) => cookie.name === 'access_token');

          if (access_token) {
            resolve(access_token.value);
          } else {
            reject(new Error('Access token not found in JSON data.'));
          }
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  async isTokenExpired(token: string): Promise<boolean> {
    try {
      const decodedToken: any = jwt.decode(token);

      if (!decodedToken || !decodedToken.exp) {
        return true;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return decodedToken.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  async getTokenByEmailViaWebBrowser(email: string, password: string, reLogin = false): Promise<string> {

    // Check if the storage state file exists.
    const exists = fs.existsSync('storageState.json');

    if (exists && reLogin == false) {
      const token = await this.readAccessToken('storageState.json')
      const tokenStatus = await this.isTokenExpired(token)
      if (token != "" && tokenStatus == false) {
        this.contextManager.setAccessToken(token);
        return token;
      }
    }
    const browser: Browser = await chromium.launch();
    const context: BrowserContext = await browser.newContext();
    const page = await context.newPage();

    await page.goto(this._url);

    const homePage = new HomePage(page);
    await homePage.acceptPdpa();
    await homePage.navbar.accessLoginPage();
    const loginPage = new LoginPage(page);
    const token = await loginPage.getTokenByEmail(email, password);

    await page.context().storageState({ path: 'storageState.json' });

    await context.close();
    this.contextManager.setAccessToken(token);
    return token;
  }

  async getUsersImageFile(fileName: string): Promise<fs.ReadStream> {
    const imgPath = path.resolve(__dirname, "../test-data/users", fileName);
    return fs.createReadStream(imgPath);
  }

  async identityVerifications(img: any, imageType: string, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentityVerifications}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      multipart: {
        image: img,
        imageType: imageType
      }
    });
    return resp;
  }

  async identityExtractions(imageValidationId: any, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentityExtractions}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        userIdentityImageValidationId: imageValidationId,
      }
    });
    return resp;
  }

  async getImageDecryption(id: number, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointUserIdentifyImageValidation}/${id}${this.endpointDecryptionImage}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async getIdentityInformation(id: number, withToken = true): Promise<APIResponse> {

    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentityInformation}`, {
      headers: this.getHeaderForBaseUrl(withToken)
    });
    return resp;
  }

  async identityInformationValidation(firstNameTH: string, lastnameTH: string, firstnameEN: string, lastnameEN: string, fullBirthDate: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointIdentityInformationValidation}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        officialFirstNameTH: firstNameTH,
        officialLastNameTH: lastnameTH,
        officialFirstNameEN: firstnameEN,
        officialLastNameEN: lastnameEN,
        birthDate: fullBirthDate
      }
    });
    return resp;
  }

  async identityFaceComparisons(
    profile: any,
    compareUserIdentityImageValidationId: number,
    targetUserIdentityImageValidationId: number,
    enrollmentId: number,
    quizId: number,
    withToken = true
  ): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().post(`${this.baseApiUrl}${this.endpoint}${this.endpointFaceCompareValidations}`, {
      headers: this.getHeaderForBaseUrl(withToken),
      data: {
        profile: profile,
        compareUserIdentityImageValidationId: compareUserIdentityImageValidationId,
        targetUserIdentityImageValidationId: targetUserIdentityImageValidationId,
        enrollmentId: enrollmentId,
        quizId: quizId
      }
    });
    return resp;
  }
}