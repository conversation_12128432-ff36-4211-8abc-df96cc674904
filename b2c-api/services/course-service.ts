import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class CourseService extends BaseAPIService {
  readonly endpoint: string;
  readonly endpointV2: string;
  readonly endpointGetCourseSeo: string;
  readonly endpointGetCourseDetails: string;
  readonly endpointGetCourseGlances: string;
  readonly endpointGetCourseCurriculum: string;
  readonly endpointGetCourseVideoPreview: string;
  readonly endpointGetCourseReviewMe: string;
  readonly endpointGetCourseDiscussions: string;
  readonly endpointPostCourseDiscussions: string;
  readonly endpointGetCourseDiscussionDetails: string;
  readonly endpointGetCourseDiscussionReplies: string;
  readonly endpointPostCourseDiscussionReplies: string;
  readonly endpointGetCourseSuggestions: string;
  readonly endpointGetCourseSeminars: string;
  readonly endpointSearchCourseLearningPath: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/v1/courses';
    this.endpointV2 = '/v2/courses';
    this.endpointGetCourseSeo = '/seo';
    this.endpointGetCourseGlances = '/glances';
    this.endpointGetCourseCurriculum = '/curriculum';
    this.endpointGetCourseVideoPreview = '/video/{chapterId}/preview';
    this.endpointGetCourseReviewMe = '/review/me';
    this.endpointGetCourseDiscussions = '/discussions';
    this.endpointPostCourseDiscussions = '/discussions';
    this.endpointGetCourseDiscussionDetails = '/discussions/{discussionId}';
    this.endpointGetCourseDiscussionReplies = '/discussions/{discussionId}/replies';
    this.endpointPostCourseDiscussionReplies = '/discussions/{discussionId}/replies';
    this.endpointGetCourseSuggestions = '/suggestions';
    this.endpointGetCourseSeminars = '/seminars';
    this.endpointSearchCourseLearningPath = '/search/course/learning-path';
  }

  async getAllCourse(): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}`, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseSeo(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseSeo}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseDetails(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseGlances(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseGlances}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseCurriculum(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseCurriculum}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseVideoPreview(courseId: string, chapterId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseVideoPreview.replace('{chapterId}', chapterId)}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseReviewMe(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseReviewMe}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseDiscussions(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseDiscussions}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseDiscussionDetails(courseId: string, discussionId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseDiscussionDetails.replace('{discussionId}', discussionId)}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseDiscussionReplies(courseId: string, discussionId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseDiscussionReplies.replace('{discussionId}', discussionId)}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async getCourseSuggestions(courseId: string): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}/${courseId}${this.endpointGetCourseSuggestions}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }

  async searchCourseLearningPath(): Promise<APIResponse> {
    const url = `${this.baseApiUrl}${this.endpointV2}${this.endpointSearchCourseLearningPath}`;
    const resp = await this.contextManager.getContext().get(url, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }
}
