import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class SectionsService extends BaseAPIService {
  readonly dataEndPoint: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/v1/sections';
    this.dataEndPoint = 'data';
  }

  async getData(): Promise<APIResponse> {
    const resp = await this.contextManager.getContext().get(`${this.baseApiUrl}${this.endpoint}/${this.dataEndPoint}`, {
      headers: this.getHeaderForBaseUrl(),
    });
    return resp;
  }
}
