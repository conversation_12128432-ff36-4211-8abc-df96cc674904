name: B2C E2E Test
run-name: 'B2C-E2E-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'

on:
  workflow_dispatch:
    inputs:
      app_url:
        description: "APP URL, to run tests against"
        required: true
        default: https://pre-prod-web-b2c.skilllane.net
      workers:
        description: "Workers"
        required: true
        default: "4"
      action_timeout:
        description: "Action timeout in seconds (e.g., click, type)"
        required: false
        default: "30"
      navigation_timeout:
        description: "Navigation timeout in seconds"
        required: false
        default: "30"
      expect_timeout:
        description: "Expect assertion timeout in seconds"
        required: false
        default: "30"
  repository_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write
  checks: write
  security-events: write
  
concurrency:
  group: b2c-e2e
  cancel-in-progress: true

env:
  ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN }}
  ZEPHYR_TEST_CYCLE_NAME: 'B2C-E2E-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'
  ZEPHYR_FOLDER_ID: 7797324
  SLACK_WEBHOOK_URL: ${{ secrets.B2C_SLACK_WEBHOOK_URL }}
  APP_URL: ${{ github.event.inputs.app_url || 'https://pre-prod-web-b2c.skilllane.net' }}
  WORKERS: ${{ github.event.inputs.workers || '4' }}
  ACTION_TIMEOUT: ${{ github.event.inputs.action_timeout || '30' }}000
  NAVIGATION_TIMEOUT: ${{ github.event.inputs.navigation_timeout || '30' }}000
  EXPECT_TIMEOUT: ${{ github.event.inputs.expect_timeout || '30' }}000

jobs:
  install:
    timeout-minutes: 30
    runs-on: skl-runner
    outputs:
      playwright_version: ${{ steps.set-env.outputs.PLAYWRIGHT_VERSION }}
      started_at: ${{ steps.set-env.outputs.STARTED_AT}}
      app_url: ${{ steps.set-env.outputs.APP_URL}}
      pull_request_url: ${{ steps.set-env.outputs.PULL_REQUEST_URL }}
      workers: ${{ steps.set-env.outputs.WORKERS}}

    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install -y git-lfs
          git lfs install

      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
          lfs: true

      - name: Set current date as env variable
        id: started-at
        run: |
          echo "STARTED_AT=$(date +%s)" >> $GITHUB_ENV
          echo "STARTED_AT=$(date +%s)" >> $GITHUB_OUTPUT

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Get installed Playwright version
        id: playwright-version
        run: |
          PLAYWRIGHT_VERSION=$(node -e "console.log(require('./package-lock.json').packages['node_modules/@playwright/test'].version)")
          echo "PLAYWRIGHT_VERSION=${PLAYWRIGHT_VERSION}" >> $GITHUB_ENV
          echo "PLAYWRIGHT_VERSION=${PLAYWRIGHT_VERSION}" >> $GITHUB_OUTPUT

      - name: Cache playwright binaries
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - name: Install Playwright browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: npx playwright install --with-deps

      - name: Install dependencies
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: npm ci --ignore-scripts
      
      - name: Create Output for ENV Variables
        id: set-env
        run: |
          echo "PLAYWRIGHT_VERSION=${PLAYWRIGHT_VERSION}" >> $GITHUB_OUTPUT
          echo "STARTED_AT=${STARTED_AT}" >> $GITHUB_OUTPUT   
          echo "APP_URL=${APP_URL}" >> $GITHUB_OUTPUT        
          echo "WORKERS=${WORKERS}" >> $GITHUB_OUTPUT        
          echo "PULL_REQUEST_URL=${PULL_REQUEST_URL:-}" >> $GITHUB_OUTPUT

  tests:
    name: Run Playwright Tests (${{ matrix.shardIndex }}/${{ matrix.shardTotal }})
    needs: install
    timeout-minutes: 30
    runs-on: skl-runner
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [1, 2, 3, 4]
        shardTotal: [4]

    env:
      PLAYWRIGHT_VERSION: ${{ needs.install.outputs.playwright_version }}
      STARTED_AT: ${{ needs.install.outputs.started_at }}
      WORKERS: ${{ needs.install.outputs.workers }}
      PULL_REQUEST_URL: ${{ needs.install.outputs.pull_request_url }}
      APP_URL: ${{ needs.install.outputs.app_url }}

    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install -y git-lfs

      - uses: actions/checkout@v4
        with:
          lfs: true
          fetch-depth: 1

      - name: Git LFS Pull
        run: git lfs pull

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Restore Cache Playwright
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - name: Install dependencies
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: npm ci --ignore-scripts

      - name: Install Chrome Browser
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: npx playwright install chrome
      
      - name: Update app config
        run: echo '${{ vars.B2C_E2E_CONFIG }}' > b2c-e2e/configurations/app-settings.json
    
      - name: Create report directories
        run: |
          mkdir -p playwright-report
          mkdir -p test-results

      - name: Run Playwright tests
        run: APP_URL=${{ env.APP_URL}} npx playwright test ./b2c-e2e/tests/e2e_regression_test --project Chrome --config=./b2c-e2e/playwright.config.ts --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} --workers=${{ env.WORKERS}}

      - name: Copy Summary to playwright-report/ folder
        if: always()
        run: cp summary.json playwright-report/summary.json
        
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: report-${{ matrix.shardIndex }}
          path: playwright-report/
          retention-days: 3

  merge:
    name: Merge Reports
    if: always()
    needs: [install, tests]
    timeout-minutes: 30
    runs-on: skl-runner
    permissions:
      actions: read
      id-token: write
      contents: read
      issues: write
      pull-requests: write
    concurrency:
      group: "pages"
      cancel-in-progress: true

    env:
      PLAYWRIGHT_VERSION: ${{ needs.install.outputs.playwright_version }}
      STARTED_AT: ${{ needs.install.outputs.started_at }}
      APP_URL: ${{ needs.install.outputs.app_url }}
      PULL_REQUEST_URL: ${{ needs.install.outputs.pull_request_url }}

    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install -y git-lfs

      - uses: actions/checkout@v4
        with:
          lfs: true
          fetch-depth: 1

      - name: Git LFS Pull
        run: git lfs pull

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: npm ci --ignore-scripts

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: playwright-report/      
      
      - name: Display structure of downloaded files
        run: ls -R

      - name: Remove Previous html-report directory if exists
        run: rm -rf playwright-report/html-report

      - name: Run Report Merge
        run: npm run merge

      - name: Display structure of Merged
        run: ls -R

      - name: Read Summary Report to Get Test Results
        if: always()
        run: |
          STATUS=$(cat ./summary.json | jq -r '.status')
          STATUS="$(echo $STATUS | sed 's/failed/failure/;s/passed/success/')"
          echo "STATUS=$STATUS" >> $GITHUB_ENV 
          PASSED=$(cat ./summary.json | jq -r '.passed[]' | tr '\n' ' ')
          echo "PASSED=$PASSED" >> $GITHUB_ENV 
          TIMEOUT=$(cat ./summary.json | jq -r '.timedOut[]' | tr '\n' ' ' | sed 's/ /--->TIMEOUT /g')
          FAILURES=$(cat ./summary.json | jq -r '.failed[]' | tr '\n' ' ')
          FAILURES+=$TIMEOUT
          echo "FAILURES=$FAILURES" >> $GITHUB_ENV
          # Combine flakey and timedOut tests
          FLAKEY=$(cat ./summary.json | jq -r '(.flakey + .timedOut)[]' | tr '\n' ' ')
          echo "FLAKEY=$FLAKEY" >> $GITHUB_ENV

      - name: Copy Summary to html-report/ folder
        if: always()
        run: |
          mkdir -p html-report
          cp summary.json html-report/summary.json

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: html-report
          path: html-report/
          retention-days: 3

      - name: Output time taken
        if: always()
        run: |
          echo "Duration: $(($(($(date +%s) - ${{ env.STARTED_AT }}))/60)) minute(s)"
          echo "DURATION=$(($(($(date +%s) - ${{ env.STARTED_AT }}))/60))" >> $GITHUB_ENV

      - name: Show Variable status
        if: always()
        run: |
          echo "Status: ${{ env.STATUS }}"

      - name: Show Variable DURATION
        if: always()
        run: |
          echo "Duration: ${{ env.DURATION }} minute(s)"

      - name: Show Variable PASSED
        if: always()
        run: |
          echo "Passed tests: ${{ env.PASSED }}"

      - name: Show Variable FAILURES
        if: always()
        run: |
          echo "Failed tests: ${{ env.FAILURES }}"

      - name: Show Variable FLAKEY
        if: always()
        run: |
          echo "Flakey tests: ${{ env.FLAKEY }}"

      - name: Send Slack Notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: repo,eventName,workflow,job,took
          custom_payload: |
            {
              attachments: [{
                color: '${{ env.STATUS }}' === 'success' ? 'good' : 'danger',
                title: ":bomb::boom: B2C E2E Automate :boom::bomb:",
                fields: [{
                  title: 'Site Under Test',
                  value: '${{ env.APP_URL }}',
                  short: true
                },
                {
                  title: 'Triggered By',
                  value: [{'origin': 'pull_request', 'new': 'Pull Request'}, {'origin': 'schedule', 'new': 'Schedule'}, {'origin': 'repository_dispatch', 'new': 'Deploy'}, {'origin': 'workflow_dispatch', 'new': 'GitHub Actions'}].find(item => item.origin === `${process.env.AS_EVENT_NAME}`).new || `${process.env.AS_EVENT_NAME}`,
                  short: true
                },
                {
                  title: 'Repo',
                  value: `${process.env.AS_REPO}`,
                  short: true
                },
                {
                  title: 'Execution Time',
                  value: `${{ env.DURATION }} minute(s)`,
                  short: true
                },
                {
                  title: 'Workflow',
                  value: `${process.env.AS_WORKFLOW}`,
                  short: true
                },
                {
                  title: 'Total Tests',
                  value: (`${{ env.FAILURES }}`.match(/.spec.ts/g) || []).length + (`${{ env.PASSED }}`.match(/.spec.ts/g) || []).length,
                  short: true
                },
                {
                  title: 'Pull Request',
                  value: `${{ env.PULL_REQUEST_URL }}`,
                  short: false
                },
                {
                  title: 'Failures',
                  value: `${{ env.FAILURES }}` === '' ? 'No failures' : `${{ env.FAILURES }}`.match(/.spec.ts/g).length > 20 ? `Too many failures to print. Please go to GitHub to see full list of failures` : '```${{ env.FAILURES }}```'.replace(/ /g, '\n'),
                  short: false
                }]
              }]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.B2C_SLACK_WEBHOOK_URL }}
          MATRIX_CONTEXT: ${{ toJson(matrix) }}

      - name: Upload report to slack
        uses: MeilCli/slack-upload-file@v4
        id: report_html
        with:
          slack_token: ${{ secrets.B2C_SLACK_BOT_USER_OAUTH_TOKEN }}
          channel_id: ${{ secrets.B2C_SLACK_CHANNEL_ID }}
          file_path: './html-report/index.html'
