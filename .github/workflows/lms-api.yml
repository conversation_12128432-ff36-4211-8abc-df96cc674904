name: LMS API Test
run-name: 'LMS-API-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop
    types:
      - opened
      - reopened
      - synchronize
    paths:
      - 'lms-e2e/tests/tli/**'
      - 'lms-e2e/tests/cpd/**'
      - 'lms-api/tests/**'
  workflow_dispatch:
  repository_dispatch:
    types: lms-api-test
  schedule:
    - cron: '0 2 * * 1-5' # UTC time = 9 am Monday - Friday BKK

concurrency:
  group: lms-api
  cancel-in-progress: false

env:
  ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN }}
  ZEPHYR_TEST_CYCLE_NAME: 'LMS-API-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'
  ZEPHYR_FOLDER_ID: 7791572

jobs:
  playwright:
    name: 'Playwright Tests'
    runs-on: skl-runner
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install app dependencies
        run: |
          sudo apt-get update
          sudo apt-get install wget
      - name: Install dependencies
        run: |
          npm ci
          npx playwright install chrome
          npx playwright install --with-deps
      - name: Update app config
        run: echo '${{ vars.LMS_API_CONFIG }}' > lms-api/configurations/app-settings.json
      - name: Run your tests
        run: export TZ='Asia/Bangkok'; npm run test:lms-api
      - name: Read Summary Report to Get Test Results
        if: always()
        run: |
          PASSED=$(cat ./lms-api/test-results/summary.json | jq -r '.stats.expected')
          echo "PASSED=$PASSED" >> $GITHUB_ENV
          FAILURES=$(cat ./lms-api/test-results/summary.json | jq -r '.stats.unexpected')
          echo "FAILURES=$FAILURES" >> $GITHUB_ENV
          FLAKY=$(cat ./lms-api/test-results/summary.json | jq -r '.stats.flaky')
          echo "FLAKY=$FLAKY" >> $GITHUB_ENV
          TOTAL_TEST=$(expr $PASSED + $FAILURES + $FLAKY)
          echo "TOTAL_TEST=$TOTAL_TEST" >> $GITHUB_ENV
      - name: Send Slack Notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          job_name: 'Playwright Tests'
          fields: repo,eventName,workflow,job,took
          custom_payload: |
            {
              attachments: [{
                color: '${{ job.status }}' === 'success' ? 'good' : 'danger',
                title: `LMS API automate test result :see_no_evil:`,
                fields: [{
                  title: 'Site Under Test',
                  value: 'https://lms-api-staging.skilllane.net',
                  short: true
                },
                {
                  title: 'Triggered By',
                  value: (() => {
                    const event = [{'origin': 'pull_request', 'new': 'Pull Request'}, {'origin': 'schedule', 'new': 'Schedule'}, {'origin': 'repository_dispatch', 'new': 'Deploy'}, {'origin': 'workflow_dispatch', 'new': 'GitHub Actions'}].find(item => item.origin === `${process.env.AS_EVENT_NAME}`);
                    return event ? event.new : `${process.env.AS_EVENT_NAME}`;
                  })(),
                  short: true
                },
                {
                  title: 'Repo',
                  value: `${process.env.AS_REPO}`,
                  short: true
                },
                {
                  title: 'Execution Time',
                  value: `${process.env.AS_TOOK}`,
                  short: true
                },
                {
                  title: 'Workflow',
                  value: `${process.env.AS_WORKFLOW}`,
                  short: true
                },
                {
                  title: 'Total Tests',
                  value: `${{env.TOTAL_TEST}}`,
                  short: true
                },
                {
                  title: 'Failures',
                  value: `${{ env.FAILURES }}` === '0' ? 'No failures' : `${{ env.FAILURES }}`,
                  short: false
                }]
              }]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} # required
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report
      - name: Upload test results to Zephyr Scale
        if: always()
        run: >-
          curl --location 'https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SKL&autoCreateTestCases=false' 
          --header 'Authorization: Bearer ${{env.ZEPHYR_SCALE_TOKEN}}'
          --form 'file=@"./lms-api/test-results/api-results.xml"' 
          --form 'testCycle="{\"name\": \"${{env.ZEPHYR_TEST_CYCLE_NAME}}\", \"folderId\": \"${{env.ZEPHYR_FOLDER_ID}}\"}";type=application/json'
