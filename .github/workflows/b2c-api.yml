name: B2C API Test
run-name: 'B2C-API-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'

on:
  workflow_dispatch:
    inputs:
        app_url:
          description: "APP URL, to run tests against"
          required: true
          default: https://pre-prod-web-b2c.skilllane.net
        workers:
          description: "Workers"
          required: true
          default: "4"
  repository_dispatch:
  schedule:
     - cron: "0 1 * * *"
permissions:
  contents: write
  pages: write
  
concurrency:
  group: b2c-api

env:
  ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN }}
  ZEPHYR_TEST_CYCLE_NAME: 'B2C-API-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'
  ZEPHYR_FOLDER_ID: 7797324
  SLACK_WEBHOOK_URL: ${{ secrets.B2C_SLACK_WEBHOOK_URL }}

jobs:
  install:
    permissions:
      contents: read
      pages: write      
      id-token: write  
    timeout-minutes: 60
    runs-on: skl-runner
    outputs:
      playwright_version: ${{ steps.set-env.outputs.PLAYWRIGHT_VERSION }}
      started_at: ${{ steps.set-env.outputs.STARTED_AT}}
      app_url: ${{ steps.set-env.outputs.APP_URL}}
      pull_request_url: ${{ steps.set-env.outputs.PULL_REQUEST_URL }}
      workers: ${{ steps.set-env.outputs.WORKERS}}

    env:
      APP_URL: ${{ github.event.inputs.app_url }}
      WORKERS: ${{ github.event.inputs.workers }}
      PULL_REQUEST_URL: ${{ github.event.pull_request._links.html.href }}

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
  
      - name: Set current date as env variable
        run: echo "STARTED_AT=$(date +%s)" >> $GITHUB_ENV

      - run: npx playwright install --with-deps
        if: steps.playwright-cache.outputs.cache-hit != 'true'

      - name: Get installed Playwright version
        id: playwright-version
        run: echo "PLAYWRIGHT_VERSION=$(node -e "console.log(require('./package-lock.json').packages['node_modules/@playwright/test'].version)")" >> $GITHUB_ENV

      - name: Cache playwright binaries
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: |
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - name: Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: |
            node_modules
          key: modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
              
      - run: sudo npm ci --ignore-scripts
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
      
      - name: Create Output for ENV Variables
        id: set-env
        run: |
          echo "PLAYWRIGHT_VERSION=${{env.PLAYWRIGHT_VERSION}}" >> $GITHUB_OUTPUT
          echo "STARTED_AT=${{env.STARTED_AT}}" >> $GITHUB_OUTPUT   
          echo "APP_URL=${{env.APP_URL}}" >> $GITHUB_OUTPUT        
          echo "WORKERS=${{env.WORKERS}}" >> $GITHUB_OUTPUT        
          echo "PULL_REQUEST_URL=${{env.PULL_REQUEST_URL}}" >> $GITHUB_OUTPUT
  tests:
    name: Run Playwright Tests (${{ matrix.shardIndex }}/${{ strategy.job-total }})
    needs: install
    timeout-minutes: 60
    runs-on: skl-runner
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [1,2,3,4,5]
        shardTotal: [5]
    outputs:
      playwright_version: ${{ steps.set-env.outputs.PLAYWRIGHT_VERSION }}
      started_at: ${{ steps.set-env.outputs.STARTED_AT}}
      app_url: ${{ steps.set-env.outputs.APP_URL}}
      workers: ${{ steps.set-env.outputs.WORKERS}}
      pull_request_url: ${{ steps.set-env.outputs.PULL_REQUEST_URL }}

    env:
      PLAYWRIGHT_VERSION: ${{ needs.install.outputs.playwright_version }}
      STARTED_AT: ${{ needs.install.outputs.started_at }}
      WORKERS: ${{ needs.install.outputs.workers }}
      PULL_REQUEST_URL: ${{ needs.install.outputs.pull_request_url }}

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Restore Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: |
            node_modules
          key: modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
    
      - name: Restore Cache Playwright
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - run: sudo npm ci --ignore-scripts
        if: steps.node-modules-cache.outputs.cache-hit != 'true'

      - name: Set APP_URL if not passed in
        if: env.APP_URL == null
        run: | 
          echo "APP_URL=https://pre-prod-web-b2c.skilllane.net" >> $GITHUB_ENV 
      - name: Set WORKERS if not passed in
        if: env.WORKERS == null
        run: | 
          echo "WORKERS=2" >> $GITHUB_ENV 
      
      - name: Update app config
        run: echo '${{ vars.B2C_API_CONFIG }}' > b2c-api/configurations/app-settings.json
      
      - name: Run Playwright tests
        run: APP_URL=${{ env.APP_URL}} npx playwright test ./b2c-api/tests/API --project Chrome --config=./b2c-api/playwright.config.ts --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} --workers=${{ env.WORKERS}}

      - name: Copy Summary to playwright-report/ folder
        if: always()
        run: cp summary.json playwright-report/summary.json
        
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: report-${{ matrix.shardIndex }}
          path: playwright-report/
          retention-days: 3

      - name: Create Output for ENV Variables
        if: always()
        id: set-env
        run: |
          echo "PLAYWRIGHT_VERSION=${{env.PLAYWRIGHT_VERSION}}" >> $GITHUB_OUTPUT
          echo "STARTED_AT=${{env.STARTED_AT}}" >> $GITHUB_OUTPUT   
          echo "APP_URL=${{env.APP_URL}}" >> $GITHUB_OUTPUT     
          echo "PULL_REQUEST_URL=${{env.PULL_REQUEST_URL}}" >> $GITHUB_OUTPUT

  merge:
    name: Merge Reports
    if: ${{ always() }}
    needs: [install,tests]
    timeout-minutes: 60
    permissions:
      actions: read
      id-token: write
      contents: read
      issues: write
      pull-requests: write
    concurrency:
      group: "pages"
      cancel-in-progress: true
    env:
      PLAYWRIGHT_VERSION: ${{ needs.tests.outputs.playwright_version }}
      STARTED_AT: ${{ needs.tests.outputs.started_at }}
      APP_URL: ${{ needs.tests.outputs.app_url }}
      PULL_REQUEST_URL: ${{ needs.tests.outputs.pull_request_url }}
    runs-on: skl-runner
    
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Restore Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: |
            node_modules
          key: modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - run: sudo npm ci --ignore-scripts
        if: steps.node-modules-cache.outputs.cache-hit != 'true'

      - uses: actions/download-artifact@v4
        with:
          path: playwright-report/      
      
      - name: Display structure of downloaded files
        run: ls -R

      - name: Remove Previous html-report directory if exists
        run: rm -rf playwright-report/html-report

      - name: Run Report Merge
        run: npm run merge

      - name: Display structure of Merged
        run: ls -R

      - name: Read Summary Report to Get Test Results
        if: always()
        run: |
          STATUS=$(cat ./summary.json | jq -r '.status')
          STATUS="$(echo $STATUS | sed 's/failed/failure/;s/passed/success/')"
          echo "STATUS=$STATUS" >> $GITHUB_ENV 
          PASSED=$(cat ./summary.json | jq -r '.passed[]' | tr '\n' ' ')
          echo "PASSED=$PASSED" >> $GITHUB_ENV 
          TIMEOUT=$(cat ./summary.json | jq -r '.timedOut[]' | tr '\n' ' ' | sed 's/ /--->TIMEOUT /g')
          FAILURES=$(cat ./summary.json | jq -r '.failed[]' | tr '\n' ' ')
          FAILURES+=$TIMEOUT
          echo "FAILURES=$FAILURES" >> $GITHUB_ENV

      - name: Copy Summary to html-report/ folder
        if: always()
        run: cp summary.json html-report/summary.json

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: html-report
          path: html-report/
          retention-days: 3
      - name: Output time taken
        if: always()
        run: |
         echo "Duration: $(($(($(date +%s) - ${{ env.STARTED_AT }}))/60)) minute(s)"
         echo "DURATION=$(($(($(date +%s) - ${{ env.STARTED_AT }}))/60))" >> $GITHUB_ENV
      - name: Show Variable status
        if: always()
        run: |
          echo "My Variable is: ${{ env.STATUS }}"
      - name: Show Variable DURATION
        if: always()
        run: |
          echo "My Variable is: ${{ env.DURATION }}"
      - name: Show Variable PASSED
        if: always()
        run: |
          echo "My Variable is: ${{ env.PASSED }}"
      - name: Show Variable FAILURES
        if: always()
        run: |
          echo "My Variable is: ${{ env.FAILURES }}"
      - name: Send Slack Notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: repo,eventName,workflow,job,took
          custom_payload: |
            {
              attachments: [{
                color: '${{ env.STATUS }}' === 'success' ? 'good' : 'danger',
                title: `:amaze::amaze: B2C API Automate :amaze::amaze:`,
                fields: [{
                  title: 'Site Under Test',
                  value: '${{ env.APP_URL }}',
                  short: true
                },
                {
                  title: 'Triggered By',
                  value: [{'origin': 'pull_request', 'new': 'Pull Request'}, {'origin': 'schedule', 'new': 'Schedule'}, {'origin': 'repository_dispatch', 'new': 'Deploy'}, {'origin': 'workflow_dispatch', 'new': 'GitHub Actions'}].find(item => item.origin === `${process.env.AS_EVENT_NAME}`).new || `${process.env.AS_EVENT_NAME}`,
                  short: true
                },
                {
                  title: 'Repo',
                  value: `${process.env.AS_REPO}`,
                  short: true
                },
                {
                  title: 'Execution Time',
                  value: `${{ env.DURATION }} minute(s)`,
                  short: true
                },
                {
                  title: 'Workflow',
                  value: `${process.env.AS_WORKFLOW}`,
                  short: true
                },
                {
                  title: 'Total Tests',
                  value: (`${{ env.FAILURES }}`.match(/.spec.ts/g) || []).length + (`${{ env.PASSED }}`.match(/.spec.ts/g) || []).length,
                  short: true
                },
                {
                  title: 'Pull Request',
                  value: `${{ env.PULL_REQUEST_URL }}`,
                  short: false
                },
                {
                  title: 'Failures',
                  value: `${{ env.FAILURES }}` === '' ? 'No failures' : `${{ env.FAILURES }}`.match(/.spec.ts/g).length > 20 ? `Too many failures to print. Please go to GitHub to see full list of failures` : '```${{ env.FAILURES }}```'.replace(/ /g, '\n'),
                  short: false
                }]
              }]
            }  
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.B2C_SLACK_WEBHOOK_URL }}
          MATRIX_CONTEXT: ${{ toJson(matrix) }}

      - name: Upload report to slack
        uses: MeilCli/slack-upload-file@v4
        id: report_html
        with:
          slack_token: ${{ secrets.B2C_SLACK_BOT_USER_OAUTH_TOKEN }}
          channel_id: ${{ secrets.B2C_SLACK_CHANNEL_ID }}
          file_path: './html-report/index.html'
