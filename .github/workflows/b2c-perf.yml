name: B2C Performance Test
run-name: 'B2C-Performance-Test #${{ github.run_number }} - ${{ github.ref_name }}'

on:
  workflow_dispatch:
  repository_dispatch:
  # schedule:
  #   - cron: "*/30 * * * *"
permissions:
  contents: write
  pages: write
  
concurrency:
  group: b2c-perf

env:
  SLACK_WEBHOOK_URL: ${{ secrets.B2C_SLACK_WEBHOOK_URL }}

jobs:
  Main:
    runs-on: skl-runner
    steps:
      - uses: actions/checkout@v4
      - name: Run JMeter Tests
        uses: QAInsights/PerfAction@v5.6.2
        with:
          test-plan-path: ./b2c-perf/identity-extractions.jmx
          args: ''
      - name: Upload Results
        uses: actions/upload-artifact@v4
        with:
          name: jmeter-results
          path: result.jtl
      # - name: Analyze Results with Latency Lingo
      #   uses: latency-lingo/github-action@v0.0.2
      #   with:
      #     api-key: ${{ secrets.B2C_LATENCY_LINGO_API_KEY }}
      #     file: result.jtl
      #     label: Checkout Flow Automated Test Plan
      #     format: jmeter
