name: LXP API Test
run-name: 'LXP-API-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'

on:
  workflow_dispatch:

concurrency:
  group: lxp-api

env:
  ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN }}
  ZEPHYR_TEST_CYCLE_NAME: 'LXP-API-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'
  ZEPHYR_FOLDER_ID: 7797322

jobs:
  playwright:
    name: 'Playwright Tests'
    runs-on: ubuntu-latest
    #    container:
    #      image: mcr.microsoft.com/playwright:v1.31.2-focal
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Install Playwright Browsers
        run: npx playwright install --with-deps
      - name: Update app config
        run: |
          echo '${{ vars.LXP_API_CONFIG }}' > lxp-api/configurations/app-settings.json
      #          echo '${{ vars.LXP_AIRFLOW }}' > lxp-api/configurations/airflow.json
      - name: Connect VPN
        uses: aduriseti/ovpn3-connect-action@v1
        with:
          ovpn-config: ${{ secrets.OVPN_CONFIG }}
          vpn-user: ${{ secrets.OVPN_USERNAME }}
          vpn-pass: ${{ secrets.OVPN_PASSWORD }}
      - name: Run your tests
        run: export TZ='Asia/Bangkok'; npm run test:lxp-api
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report
      #      - name: Upload test results to Zephyr Scale
      #        if: always()
      #        run: >-
      #          curl --location 'https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SKL&autoCreateTestCases=false'
      #          --header 'Authorization: Bearer ${{env.ZEPHYR_SCALE_TOKEN}}'
      #          --form 'file=@"./lxp-api/test-results/api-results.xml"'
      #          --form 'testCycle="{\"name\": \"${{env.ZEPHYR_TEST_CYCLE_NAME}}\", \"folderId\": \"${{env.ZEPHYR_FOLDER_ID}}\"}";type=application/json'
      - name: Kill VPN Connection
        if: always()
        run: |
          sudo pkill openvpn
