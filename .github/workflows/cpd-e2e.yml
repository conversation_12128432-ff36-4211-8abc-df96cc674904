name: CPD E2E Test
run-name: 'CPD-E2E-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'

on:
  workflow_dispatch:

concurrency:
  group: lms-e2e

env:
  ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN }}
  ZEPHYR_TEST_CYCLE_NAME: 'CPD-E2E-Automated-Test #${{ github.run_number }} - ${{ github.ref_name }}'
  ZEPHYR_FOLDER_ID: 7797326

jobs:
  playwright:
    name: 'Playwright Tests'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Install Playwright Browsers
        run: npx playwright install --with-deps
      - name: Install Chrome Browser
        run: npx playwright install chrome
      - name: Update app config
        run: echo '${{ vars.CPD_E2E_CONFIG }}' > cpd-e2e/configurations/app-settings.json
      - name: Get large assets
        run: wget -P ./shared/ekyc https://skilllane-cpd-staging.s3.ap-southeast-1.amazonaws.com/assets/valid-face.y4m
      - name: Connect VPN
        uses: aduriseti/ovpn3-connect-action@v1
        with:
          ovpn-config: ${{ secrets.OVPN_CONFIG_2 }}
          vpn-user: ${{ secrets.OVPN_USERNAME_2 }}
          vpn-pass: ${{ secrets.OVPN_PASSWORD_2 }}
      - name: Run your tests
        run: export TZ='Asia/Bangkok'; npm run test:cpd-e2e-web
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report

      #      - name: Upload test results to Zephyr Scale
      #        if: always()
      #        run: >-
      #          curl --location 'https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SKL&autoCreateTestCases=false'
      #          --header 'Authorization: Bearer ${{env.ZEPHYR_SCALE_TOKEN}}'
      #          --form 'file=@"./cpd-e2e/test-results/e2e-results.xml"'
      #          --form 'testCycle="{\"name\": \"${{env.ZEPHYR_TEST_CYCLE_NAME}}\", \"folderId\": \"${{env.ZEPHYR_FOLDER_ID}}\"}";type=application/json'
      - name: Kill VPN Connection
        if: always()
        run: |
          sudo pkill openvpn
        shell: bash
