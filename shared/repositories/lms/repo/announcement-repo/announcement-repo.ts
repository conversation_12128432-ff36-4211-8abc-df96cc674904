import { AnnouncementsModel } from '../../models/announcement-models/announcement-model';

export class AnnouncementRepo {
  readonly mongoose: typeof import('mongoose');

  constructor(mongoose: typeof import('mongoose')) {
    this.mongoose = mongoose;
  }

  async getAnnouncementById(id: string) {
    return AnnouncementsModel.findOne({ id: id });
  }

  async deleteAnnouncementById(id: string) {
    return AnnouncementsModel.deleteOne({ id: id });
  }

  async deleteManyAnnouncementsById(id: string) {
    return AnnouncementsModel.deleteMany({ id: id });
  }
}
