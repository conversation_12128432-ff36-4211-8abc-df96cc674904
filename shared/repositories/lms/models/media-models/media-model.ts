import { model, ObjectId, Schema } from 'mongoose';

export interface Media {
  _id: ObjectId;
  id: string;
  organizationId: string;
  type: string;
  filename: string;
  path: string;
  mimeType: string;
  size: ObjectId;
  metadata: string;
  createdAt: Date;
  updatedAt: Date;
}

const schema = new Schema<Media>(
  {
    id: { type: Schema.Types.String },
    organizationId: { type: Schema.Types.String },
    type: { type: Schema.Types.String },
    filename: { type: Schema.Types.String },
    path: { type: Schema.Types.String },
    mimeType: { type: Schema.Types.String },
    metadata: { type: Schema.Types.String },
  },
  { versionKey: false },
);

export const MediaModel = model<Media>('media', schema);
