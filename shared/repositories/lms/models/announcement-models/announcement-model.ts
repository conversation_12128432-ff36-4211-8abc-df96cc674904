import { ObjectId, Schema, model } from 'mongoose';

export interface Announcements {
  _id: ObjectId;
  id: String;
  organizationId: String;
  title: String;
  thumbnailId: String;
  thumbnailUrl: String;
  publishedStartAt: Date;
  publishedEndAt: Date;
  authors: [];
  isEnabled: Boolean;
  isHighlight: Boolean;
  highlightIndex: Number;
  attachments: [];
  createdAt: Date;
  updatedAt: Date;
}

const schema = new Schema<Announcements>(
  {
    id: { type: Schema.Types.String },
    organizationId: { type: Schema.Types.String },
    title: { type: Schema.Types.String },
    thumbnailId: { type: Schema.Types.String },
    thumbnailUrl: { type: Schema.Types.String },
    publishedStartAt: { type: Schema.Types.Date },
    publishedEndAt: { type: Schema.Types.Date },
    authors: [],
    isEnabled: { type: Schema.Types.Boolean },
    isHighlight: { type: Schema.Types.Boolean },
    highlightIndex: { type: Schema.Types.Number },
    attachments: [],
    createdAt: { type: Schema.Types.Date },
    updatedAt: { type: Schema.Types.Date },
  },
  { versionKey: false },
);
export const AnnouncementsModel = model<Announcements>('announcements', schema);
