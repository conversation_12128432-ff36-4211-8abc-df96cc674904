import { ObjectId, Schema, model } from 'mongoose';

export interface ReportHistories {
  _id: ObjectId;
  id: String;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  organizationId: String;
  userId: String;
  status: String;
  type: String;
  fileName: String;
  downloadFileType: String;
  files: [];
}

const schema = new Schema<ReportHistories>(
  {
    id: { type: Schema.Types.String },
    createdAt: { type: Schema.Types.Date },
    updatedAt: { type: Schema.Types.Date },
    deletedAt: { type: Schema.Types.Date },
    organizationId: { type: Schema.Types.String },
    userId: { type: Schema.Types.String },
    status: { type: Schema.Types.String },
    type: { type: Schema.Types.String },
    fileName: { type: Schema.Types.String },
    downloadFileType: { type: Schema.Types.String },
    files: [],
  },
  { versionKey: false },
);
export const ReportHistoriesModel = model<ReportHistories>('report-histories', schema);
