pipeline {
  agent { node { label "slave-tester" } }

  parameters {
    string(name: 'BRANCH', defaultValue: 'new-jenkins-script')
    string(name: 'RUN_ARGS', defaultValue: '', description: 'Extra run params. EX: -g "Employee login success"')
    booleanParam(name: 'DEBUG', defaultValue: false, description: 'Change to true for testing')
  }
  
  environment {
    PROJECT_NAME = "b2c-api"

    BRANCH = "${params.BRANCH}"
    RUN_ARGS = "${params.RUN_ARGS != '' ? ' -- ' + params.RUN_ARGS : ' '}" 
    SLACK_CHANNEL = "#deployments"
    GIT_REPO_URL = "*****************:skilllane/skilllane-playwright.git"
    GIT_CRED_ID = "skl-bitbucket-ssh"
    DATE = new Date().format("YYYY-MM-dd")
    GCP_STORAGE_PATH = "/playwright-result"
    RCLONE_DESTINATION = "GS-CORE-PLATFORM"
  }

  stages {
  
    stage("Execute Playwright test") { 
      agent {
        dockerfile {
          filename 'Dockerfile'
          label "slave-tester"
          args '-u root'
          reuseNode true
        }
      }

      steps {
        script {
          // Get key vault
          sh 'npm install yarn -g'
          sh 'yarn install'
          script {
            withCredentials([string(credentialsId: 'vault-root', variable: 'TOKEN')]) {
              sh """
              TOKEN=${TOKEN} yarn run env:vault-sync
              """
            }
          }

          // Run test
          sh 'rm -rf test-results'
          sh 'rm -rf test-results-summary'
          sh 'mkdir test-results-summary'
          sh "export TZ='Asia/Bangkok'; export TEST_ENV=Test; yarn run test:b2c-api " + env.RUN_ARGS
        }
      }
      post {
        always {
          script {
            def summary = junit '**/**-results.xml'
            def color = '#3dfc70'
            if(summary.failCount > 0) {
                color = '#ff0000'
            }

            String message = """
            Job name: *${JOB_NAME}*
            Status: ${currentBuild.currentResult}
            Tests: Passed ${summary.passCount}, Fail ${summary.failCount}
            Execution Time: ${currentBuild.durationString}
            URL: <${BUILD_URL}| View build #${BUILD_NUMBER}>
            """.stripIndent();
            
            if(!params.DEBUG) {
              // slackSend channel: SLACK_CHANNEL, color: color, message: message
            }

          }
        }
      }
    }
  }
  post {
    always {
      sh 'tar --ignore-failed-read -zcvf '+ env.DATE + '-'+ env.PROJECT_NAME +'.tar.gz ./'+ env.PROJECT_NAME +'/test-results'
      sh "rclone copy \$(echo '"+ env.DATE + "-"+ env.PROJECT_NAME +".tar.gz') \$(echo '${RCLONE_DESTINATION}'):\$(echo '${GCP_STORAGE_PATH}/"+ env.PROJECT_NAME +"/')"
      cleanWs()
    }
  }
}
