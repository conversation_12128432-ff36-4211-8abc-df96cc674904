def SCM_URL = "*****************:skilllane/skilllane-playwright.git"
def BRANCH = 'develop'
def CLOUD_BUILD_CREDENTIAL = "skilllane-coned-platform"
def GCLOUD_CREDENTIAL = "skilllane-platform-gcloud-credential"
def BIT_BUCKET_CREDENTIAL = "skl-bitbucket"
def DATE = new Date().format('YYYY-MM-dd')
def GCP_STORAGE_PATH = 'playwright-result'

node('skilllane-gcloud-util') {

  stage("Init gcloud credential") {
    initDeploy('infra', GCLOUD_CREDENTIAL)
  }
  
  stage("Checkout") {
    retry (3) {
        cloneRepo(SCM_URL, BRANCH, BIT_BUCKET_CREDENTIAL)
        GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'%h'", returnStdout: true)
        GIT_COMMIT_HASH = GIT_COMMIT_HASH.trim()
    }
    
    sh 'npm install'
    sh 'npx playwright install'
    sh 'npx playwright install chrome'
  }
  
  stage("Initate Token") {
      // Install missing require library
    sh 'apt-get install -y jq'
    sh 'apt-get install -y coreutils'
    
    withCredentials([string(credentialsId: 'VAULT_ROOT', variable: 'TOKEN')]) {
        sh """
        TOKEN=${TOKEN} npm run env:vault-sync
        """
    }
    
  }
  
  try {
      stage("run playwright e2e") {
          print(params.PROJECT)
          if (params.COMMAND != '' ) {
              sh "${params.COMMAND}"
          } else {
              try {
                sh "export TEST_ENV=Test"
                sh "npm run test:${params.PROJECT}"
              } catch (e) {}

              sh"""
              tar --ignore-failed-read -zcvf ${DATE}-${params.PROJECT}.tar.gz test-results 
              gsutil cp \$(echo '${DATE}-${params.PROJECT}.tar.gz') gs://$GCP_STORAGE_PATH/${params.PROJECT}/
              
              """        
          }
          
          junit '**/e2e-results.xml'
          
          // slackSend channel: params.SLACK_CHANNEL, color: "#3dfc70", message: "${params.PROJECT} playwright passed"
      }
  } catch (e) {
      println(e)
      // slackSend channel: params.SLACK_CHANNEL, color: "#ff0000", message: "${params.PROJECT} playwright failed"
  }
}