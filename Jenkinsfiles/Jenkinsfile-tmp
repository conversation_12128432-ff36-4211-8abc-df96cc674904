def SCM_URL = "https://<EMAIL>/skilllane/skilllane-playwright.git"
def BRANCH = 'master'
def CLOUD_BUILD_CREDENTIAL = "skilllane-coned-platform"
def GCLOUD_CREDENTIAL = "skilllane-coned-platform-gcloud-credential"
def BIT_BUCKET_CREDENTIAL = "bitbucket-dev-credential"
def DATE = '-' + new Date().format('YYYY-MM-dd')
def GCP_STORAGE_PATH = 'playwright-result'

node('cypress'){
  stage("Checkout") {
    retry (3) {
        cloneRepo(SCM_URL, <PERSON>ANCH, BIT_BUCKET_CREDENTIAL)
        GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'%h'", returnStdout: true)
        GIT_COMMIT_HASH = GIT_COMMIT_HASH.trim()
    }
    
    sh 'npm install'
    sh 'npx playwright install'
  }
  
  stage("Initate Token") {
      // withCredentials([string(credentialsId: 'VAULT_ROOT', variable: 'TOKEN')]) {
          // sh """
          // TOKEN=${TOKEN} npm run env:vault-sync
          // """
      // }
  }
  
  try {
      stage("run playwright e2e") {
          print(params.PROJECT)
          if (params.COMMAND != '' ) {
              sh "${params.COMMAND}"
              
          } else {
              try {
                sh "npm run test:${params.PROJECT}"
              } catch (e) {}

              sh"""
              tar --ignore-failed-read -zcvf ${DATE}-${params.PROJECT}.tar.gz test-results playwright-report
              gsutil cp \$(echo '${DATE}-${params.PROJECT}.tar.gz') gs://$GCP_STORAGE_PATH/${params.PROJECT}/
              
              """        
          }
          // slackSend channel: params.SLACK_CHANNEL, color: "#3dfc70", message: "${params.PROJECT} playwright passed"
      }
  } catch (e) {
      println(e)
      // slackSend channel: params.SLACK_CHANNEL, color: "#ff0000", message: "${params.PROJECT} playwright failed"
  }
}